"""
Module to extract Chemical compound characterization data from Reaction Findings by Rules.

This module provides functionality for processing and analyzing finding types in text data.
It includes utilities for pattern matching and text cleaning to standardize finding type formats.


Author: Anand Jadhav
Date: 25-06-2025

"""
## Imports required
import os
import re
import pandas as pd
import logging
import time
from tqdm import tqdm


def replace_finding_types(data_text, ALL_FINDING_TYPES, excel_file, symbol_excel_file):
    """
        Replace finding types in the input text based on mappings from an Excel file and replace symbols.

        Parameters:
        - data_text (str): The input text to process.
        - finding_type (str): The type of finding (not used in the function body, consider removing if unnecessary).
        - excel_file (str): Path to the Excel file containing ARTICLE_VALUE to FINDING_TYPE mappings.
        - symbol_excel_file (str): Path to the Excel file containing symbol replacement information.

        Returns:
        - str: The processed text with finding types replaced and symbols cleaned.
    """

    try:
        # Clean and replace symbols in the text using a helper function
        data_text = replace_symbols_from_text(data_text, symbol_excel_file)

        # Read the Excel file
        df_cleaning = pd.read_excel(excel_file)

        # Filter out non-string values and handle NaNs
        df_cleaning = df_cleaning.dropna(subset=['ARTICLE_VALUE', 'FINDING_TYPE']) 
        df_cleaning['ARTICLE_VALUE'] = df_cleaning['ARTICLE_VALUE'].astype(str).str.strip()
        df_cleaning['FINDING_TYPE'] = df_cleaning['FINDING_TYPE'].astype(str).str.strip()

        # Create a dictionary mapping ARTICLE_VALUE to FINDING_TYPE
        replacement_dict = dict(zip(df_cleaning['ARTICLE_VALUE'], df_cleaning['FINDING_TYPE']))

        # Track replaced items to avoid redundant replacements
        replaced_items = set()

        # Compile a regex pattern to match ARTICLE_VALUE entries
        finding_pattern = '|'.join(rf'\b{re.escape(key)}\b' for key in replacement_dict.keys())

        # Replacement function to map ARTICLE_VALUE to FINDING_TYPE
        def replacement_function(match):
            matched_word = match.group(0)
            # Check if this item was already replaced
            if matched_word in replaced_items:
                # Return original if already replaced
                return matched_word
            else:
                # Mark this item as replaced and replace it with FINDING_TYPE
                replaced_items.add(matched_word)
                replacement = replacement_dict.get(matched_word, matched_word)
                # print(f" {replacement} ")

                return f" {replacement} "

        # Perform replacements in the input text
        cleaned_data_text = re.sub(finding_pattern, replacement_function, data_text)

        # Ensure only single spaces between words
        cleaned_data_text = re.sub(r'\s+', ' ', cleaned_data_text).strip()

        cleaned_data_text = match_and_clean_finding_types(cleaned_data_text, ALL_FINDING_TYPES)

        cleaned_data_text = add_spaces_around_findings(cleaned_data_text)

        # Return cleaned data or original text if no changes occurred
        return cleaned_data_text if cleaned_data_text != data_text else data_text

    except Exception as error:
        # Log the error if any exception occurs
        logging.error("replace_finding_types Error: %s", str(error))

        return data_text



def replace_symbols_from_text(data_text, symbol_excel_file):
    """
    Replace symbols in the input text with their corresponding finding types based on the provided Excel file.

    This function reads an Excel file containing symbol-to-finding type mappings and replaces occurrences
    of these symbols in the input text with their corresponding finding types.

    Args:
        data_text (str): The input text containing symbols to be replaced.
        symbol_excel_file (str): Path to the Excel file containing symbol-to-finding type mappings.

    Returns:
        str: The input text with symbols replaced by their corresponding finding types.
                If an error occurs during the process, the original input text is returned.
    """
    try:
        # Read the Excel file
        df_cleaning = pd.read_excel(symbol_excel_file)

        # Check if the necessary columns exist
        if ("FINDING_TYPE" not in df_cleaning.columns or "ARTICLE_VALUE" not in df_cleaning.columns):
            raise ValueError("symbol_excel_file must contain 'FINDING_TYPE' and 'ARTICLE_VALUE' columns." )

        # Create a mapping of FINDING_TYPE to ARTICLE_VALUE, normalize to lowercase for case-insensitivity
        replacement_dict = {
            str(key).lower(): value
            for key, value in zip(
                df_cleaning["ARTICLE_VALUE"], df_cleaning["FINDING_TYPE"]
            )
        }

        # Create a regex pattern to match whole words (exact matches) from ARTICLE_VALUE
        finding_pattern = (
            r"\b("
            + "|".join(re.escape(str(key)) for key in replacement_dict.keys())
            + r")\b"
        )

        # Function to replace matches using the replacement dictionary
        def replacement_function(match):
            # Get the matched word (key), normalize it to lowercase, and replace it using the dictionary
            matched_word = match.group(0).lower()
            return replacement_dict.get(
                matched_word, matched_word
            )  # Fallback to matched_word if not in dict

        # Replace occurrences in data_text with case-insensitivity and exact matching
        cleaned_data_text = re.sub(
            finding_pattern, replacement_function, data_text, flags=re.IGNORECASE
        )

        # If no replacement was made, return original
        return (cleaned_data_text if cleaned_data_text else data_text)
    except Exception as error:
        # Log the error if any exception occurs
        logging.error("replace_symbols_from_text Error: %s", str(error))
        return data_text



def match_and_clean_finding_types(data_text, finding_types):
    """
    Matches items from the FINDING_TYPES list with variations (spaces, lowercase, etc.) in the data_text,
    and replaces them with the clean version from the FINDING_TYPES list.

    Parameters:
    - data_text: The text where we search for matches.
    - finding_types: A list of predefined finding types to match.

    Returns:
    - A tuple containing:
      - A list of matched finding types.
      - The cleaned data_text with all matched finding types replaced.
    """
    try:
        # Normalize the input text (make it lowercase for case-insensitive matching)
        data_text_normalized = data_text.lower()

        # Create a copy of the original data_text to perform replacements
        cleaned_data_text = data_text

        # Loop through each finding type and create a flexible regex pattern
        for finding_type in finding_types:
            # Remove any existing spaces in the finding type (so we can match it flexibly)
            finding_type_cleaned = finding_type.replace(" ", "").lower()

            # Create a regex pattern that allows spaces or missing spaces between characters
            pattern = r'\b' + r'\s*'.join(re.escape(char) for char in finding_type_cleaned) + r'\b'

            # Search for this pattern in the normalized data text
            if re.search(pattern, data_text_normalized):

                # Replace the flexible match in the original text with the clean finding type
                cleaned_data_text = re.sub(pattern, finding_type, cleaned_data_text, flags=re.IGNORECASE)
        cleaned_data_text = cleaned_data_text.replace("  ", " ")

        return  cleaned_data_text
    except Exception as error:
        return data_text



def add_spaces_around_findings(data_text):
    """
    Add spaces around findings in data_text if they don't already have them.

    Args:
    - data_text (str): The text in which to check for findings.
    - finding_list (list): A list of findings to check.

    Returns:
    - str: The updated data_text with spaces added around findings.
    """
    try:
        finding_list = ["11B NMR", "13C NMR", "15N NMR", "19F NMR", "1H NMR", "29SI NMR", "31P NMR",  "CRYSTAL DATA", "ELEMENTAL ANALYSIS", "BP", "EE", "Rf", "Rt",
        "HRMS",  "m/z", "UV/vis",  "raman"]  #"[a]D", "Mp","IR"
        for finding in finding_list:
            # Create a regex pattern to find the finding without spaces around it
            pattern = r'(?<!\S)' + re.escape(finding) + r'(?!\S)'

            # Use regex sub to add spaces around the finding if not already present (case-insensitive)
            data_text = re.sub(pattern, f' {finding} ', data_text, flags=re.IGNORECASE)

        # Return the updated data_text, stripped of extra spaces
        return ' '.join(data_text.split())
    except Exception as error:
        # Log the error if any exception occurs
        logging.error("add_spaces_around_findings Error: %s", str(error))
        return data_text



def extract_percentage(text):
    """
    Extracts all percentage values from the input text and returns them as a string.
    If no percentage is found, it returns the original input text.

    Parameters:
    - text (str): The input text to search.

    Returns:
    - str: A string of percentage values separated by commas, or the original text if none are found.
    """
    try:
        # Regex pattern to match percentage values with optional comparison signs and flexible spacing
        pattern = r'[><=]?\s*\d+(?:\.\d+)?\s*%'

        # Find all occurrences of the pattern
        matches = re.findall(pattern, text)

        if matches:
            # Join matches into a single string separated by commas
            ee_match = ', '.join(match.strip() for match in matches)
            ee_match = ee_match.replace("EE", " ").replace("ee", " ").replace("e.e.", " ").replace("  ", " ").strip()
            return ee_match
        else:
            # Return original text if no percentages are found
            return text
    except Exception as error:
        # Log the error if any exception occurs
        logging.error("extract_percentage Error: %s", str(error))
        return text


def extract_ee_value(data_text):
    """
    Extract the enantiomeric excess (ee) value from the given text.

    This function searches for ee values in the provided text using a regular expression pattern.
    It handles various formats including signs, percentages, and different "EE" variations.

    Args:
        data_text (str): The text to search for ee values.

    Returns:
        tuple: A tuple containing two elements:
            - The extracted ee value as a string, or None if no value is found.
            - The input text with the ee value removed (if found).
    """
    # Regex to match EE-related values with optional signs, spaces, and percentages
    ee_pattern = r'([><=]?\s*-?\d+(?:\.\d+)?\s*%)\s*[Ee]{2}\b'

    # Find all EE matches in the text
    matches = re.findall(ee_pattern, data_text, flags=re.IGNORECASE)

    if matches:
        # Remove duplicates by converting to a set and preserve order by using dict.fromkeys
        unique_values = list(dict.fromkeys(match.strip() for match in matches))

        # Join unique values as a comma-separated string
        ee_values = ' | '.join(unique_values)

        # Remove all occurrences of the EE pattern from the text
        cleaned_text = re.sub(ee_pattern, '', data_text, flags=re.IGNORECASE).strip()

        # Return the comma-separated EE values and cleaned text
        return ee_values, cleaned_text

    # If no match is found, return None for EE value and original text
    return None, data_text




def transform_data_text_for_optical_rotation(data_text):
    """
    Ensures optical rotation data is correctly formatted by adding '[a]D'
    before the matched patterns without altering the remaining text.

    Parameters:
    - data_text (str): The original text to be transformed.

    Returns:
    - str: The transformed text with patterns prefixed by '[a]D'.
    """
    try:
        # Replace multiple spaces with a single space
        data_text = re.sub(r'\s+', ' ', data_text.strip())

        data_text = data_text.replace("[a] ", "[a]")

        data_text = data_text.replace("[α] ", "[a]")

        # Normalize multiple brackets around 'a' or 'α' to a single pair
        data_text = re.sub(r"\[+\s*([aα])\s*\]+", r"[\1]", data_text)

        # Add '[a]D' before numbers that follow '[a]' or '[α]' without 'D'
        data_text = re.sub(r"(\[[aα]\])(\d+)(?!D)", r"\1D\2", data_text)

        # Add '[a]D' before patterns like '[a]D25'
        data_text = re.sub(r"(\[[aα]\]D\s*\d+)([^\s=])", r"[a]D \1\2", data_text)

        # Add '[a]D' before any standalone instance like '[a]25589'
        data_text = re.sub(r"(\[[aα]\d+)([^\s=])", r"[a]D \1\2", data_text)

        # Add '[a]D' before '[a]D25' that could be followed by any special character
        data_text = re.sub(r"(\[[aα]\]D\s*\d+)(\s|=|$)", r"[a]D \1 ", data_text)

        # Clean up any extra spaces
        transformed_text = re.sub(r'\s+', ' ', data_text.strip())
        transformed_text = transformed_text.replace("[a]D [a]D ", "[a]D ")

        # Add this snippet at the end of your transformation function
        transformed_text = re.sub(r"(\[a\]D\d+)D", r"\1", transformed_text)
        return transformed_text.strip()

    except Exception as error:
        # Log the error if any exception occurs
        logging.error("transform_data_text_for_optical_rotation Error: %s", str(error))
        return data_text



def remove_consecutive_duplicates(data_text, FINDING_TYPES):
    """
        Remove consecutive duplicate finding types from the input text.

        This function processes the input text to remove consecutive occurrences of finding types,
        while preserving the order and non-finding type words. It normalizes the text for comparison
        and handles case-insensitive matching.

        Parameters:
        - data_text (str): The input text to process.
        - finding_types (list): A list of finding type strings to check for and remove duplicates.

        Returns:
        - str: The processed text with consecutive duplicate finding types removed.

    """
    try:
        # Create a regex pattern for matching FINDING_TYPES with optional spaces, brackets, or special characters
        pattern = re.compile(
            r'\b(' + '|'.join([re.escape(item).replace(r'\ ', r'\s*').replace(r'\[', r'[\[\(]?').replace(r'\]', r'[\]\)]?') for item in FINDING_TYPES]) + r')\b',
            re.IGNORECASE
        )

        cleaned_text = []
        # Track the last matched item (normalized for comparison)
        last_item = None
        # Track the position in the input text
        current_pos = 0

        # Iterate through all matches of FINDING_TYPES in the text
        for match in pattern.finditer(data_text):
            start, end = match.span()

            # The actual matched item
            current_item = match.group(0)

            # Normalize for comparison
            normalized_item = re.sub(r'[\s\[\]\(\)]', '', current_item).lower()

            # Add any non-matching text between the previous match and the current one
            if current_pos < start:
                cleaned_text.append(data_text[current_pos:start])

            # Only add the current item if it's not a consecutive duplicate
            if last_item != normalized_item:
                cleaned_text.append(current_item)

            # Update last item
            last_item = normalized_item

            # Move the current position to after this match
            current_pos = end

        # Add any remaining text after the last match
        if current_pos < len(data_text):
            cleaned_text.append(data_text[current_pos:])

        # Join all parts to reconstruct the cleaned text
        return ''.join(cleaned_text)

    except Exception as error:
        # Log the error if any exception occurs
        logging.error("remove_consecutive_duplicates Error: %s", str(error))
        return data_text




def remove_trailing_punctuation(df, column_name):
    """
    Remove leading and trailing punctuation (.,:;) from the specified column in the DataFrame.

    Parameters:
    - df: Input DataFrame
    - column_name: The name of the column to process

    Returns:
    - Updated DataFrame with leading and trailing punctuation removed
    """

    # Regex pattern to match leading or trailing punctuation (.,:;)
    pattern = r'^[.,:;-=_]+|[.,:;-=_]+$'

    # Apply regex substitution to remove leading and trailing punctuation from the column
    df[column_name] = df[column_name].astype(str).apply(lambda x: re.sub(pattern, '', x.strip()))

    return df



def fix_incomplete_brackets(text):
    """This function completes the opening and closing square bracket pairs in [a]D

    ### Args:
        - `text (_str_)`: data_text

    ### Returns:
        - fixed_text (_str)_: corrected data_text
    """
    # Replace the specific patterns of 'a' with '[a]'
    text = re.sub(r'(?<!\[)(a)(?=\]|\d)', r'[\1]', text)
    fixed_text = text.replace('a]]', 'a]').replace('[[a', '[a')
    return fixed_text





def get_finding_value(data_text, ALL_FINDING_TYPES, variable_excel_file, symbol_excel_file):
    try:
        data_text = str(data_text)
        if not data_text.endswith("ENDOFDATATEXT"):
            data_text += " ENDOFDATATEXT"

        data_text = transform_data_text_for_optical_rotation(data_text)
        data_text = re.sub(r'\s+', ' ', data_text).strip()
        data_text = replace_finding_types(data_text, ALL_FINDING_TYPES, variable_excel_file, symbol_excel_file)

        # Bulk replacements for normalization
        replacements = {
            "tR=": " Rt ", " tR ": " Rt ", "1H NMR": " 1H NMR ",
            "GC-MS": " m/z GC-MS ", "LC-MS": " m/z LC-MS ", "UPLC-MS": " m/z UPLC-MS ",
            " MS (ESI+)": "HRMS (ESI+) ", "HR M/Z MS": " HRMS m/z ", "M/Z": " m/z ", "LCm/z": "m/z",
            "  ": " "
        }
        for old, new in replacements.items():
            data_text = data_text.replace(old, new)

        check_duplicate_list = [
            "11B NMR", "13C NMR", "15N NMR", "19F NMR", "1H NMR", "29Si NMR", "31P NMR",
            "BP", "CRYSTAL DATA", "DE", "EE", "ELEMENTAL ANALYSIS", "HRMS", "IR",
            "m/z", "Mp", "Rf", "Rt", "UV/vis", "raman", "ENDOFDATATEXT"
        ]
        data_text = remove_consecutive_duplicates(data_text, check_duplicate_list)

        additional_replacements = {
            "HR m/z": " HRMS m/z ", "UV-VIS": " UV/vis ", "HRMSMS": " HRMS MS ",
            "MS (LIFDI, EI+)": " m/z MS (LIFDI, EI+) ", "11H-NMR:": " 1H NMR ",
            "HRESI-MS": " HRMS ESI MS ", "HR- m/z": " HRMS m/z ", "HMRESIMS:": " HRMS ESI MS ",
            "HREIMS": " HRMS EI MS ", "M.pt.": " Mp ", "  ": " "
        }
        for old, new in additional_replacements.items(): 
            data_text = data_text.replace(old, new)

        data_text = convert_rf_format(data_text)
        data_text_lower = data_text.lower()

        if "11b nmr" not in data_text_lower and "δB" in data_text:
            data_text = data_text.replace("δB", " 11B NMR δB ")
        if "13c nmr" not in data_text_lower and "δC" in data_text:
            data_text = data_text.replace("δC", " 13C NMR δC ")
        if "19f nmr" not in data_text_lower and "δF" in data_text:
            data_text = data_text.replace("δF", " 19F NMR δF ")
        if "19f nmr" not in data_text_lower:
            data_text = data_text.replace("19F", " 19F NMR ")
        if "1h nmr" not in data_text_lower and "δH" in data_text:
            data_text = data_text.replace("δH", " 1H NMR δH ")
        if "uv/vis" not in data_text_lower and "λmax" in data_text and "nm" in data_text:
            data_text = data_text.replace("λmax", "UV/vis λmax")
        elif "λ" in data_text and "nm" in data_text:
            data_text = data_text.replace("λ", " UV/vis λ ")

        data_text = re.sub(r'\s+', ' ', data_text).strip()
        ee_value, data_text = extract_ee_value(data_text)
        positions = find_item_positions(data_text, ALL_FINDING_TYPES)
        results = {}
        skip_next = False

        # ppm_pattern = re.compile(r'(?:\d+(?:\.\d+)?(?:\s*,\s*|\s+)?)+(ppm|ESI|HR|2D-NMR|LC)\b', re.IGNORECASE)
        # ppm_pattern = re.compile(r'.*\d+.*?(?:ppm|ESI|HR|2D-NMR|2D NMR|LC-|LC|MALDI|ESI|EI|)[^\w]*', re.IGNORECASE)
        # Pattern for ppm - preserves "ppm" in the result
        # Pattern for ppm - may or may not have space before "ppm", but must have space/special chars after "ppm"
        # ppm_pattern = re.compile(r'.*\d+.*?\s*(ppm[\s\W]+)', re.IGNORECASE)
        # ppm_pattern = re.compile(r'(.*\d+.*?(?:\s|^)\bppm\b(?:[\s.,;:]|$))', re.IGNORECASE)
        ppm_pattern = re.compile(r'(.*\d+.*?\s*\bppm\b[\s.,;:]*)', re.IGNORECASE)

        # Pattern for other keywords - drops them from the result
        # other_keywords_pattern = re.compile(r'.*\d+.*?(?=ESI|HR|2D-NMR|2D NMR|LC-|LC|MALDI|EI|FT|FT-|MS|HP|MS|ATR|DI|\(Fig.|FAB)', re.IGNORECASE)

        # other_keywords_pattern = re.compile(r'.*\d+.*?(?=[\s\W](?:ESI|HR|FABMS|2D-NMR|2D NMR|LC-|LC|MALDI|EI|FT|MS|HP|ATR|DI|FAB|FT-)(?:[\s\W]|$)|\(Fig\.|\(Fig.|Fig\.\*|HPm/z|DIHRMS|HPLC|HRHRMS)', re.IGNORECASE)  ##[\s\W]signals(?:[\s\W]|$)
        # other_keywords_pattern = re.compile(r'.*\d+.*?(?=[\s\W](?:ESI|HR|FABMS|2D-NMR|2D NMR|LC-|LC|MALDI|EI|FT|MS|HP|ATR|DI|FAB|FT-)(?:[\s\W]|$)|\(Fig\.|\(Fig.|Fig\.\*|HPm/z|DIHRMS|HPLC|HRHRMS|\.\s*\*\s*-)', re.IGNORECASE)
        other_keywords_pattern = re.compile(r'.*\d+.*?(?=[\s\W](?:ESI|HR|FABMS|2D-NMR|2D NMR|LC-|LC|MALDI|EI|FT|MS|HP|ATR|DI|FAB|FT-)(?:[\s\W]|$)|\(Fig\.|Fig\.|HPm/z|DIHRMS|HPLC|HRHRMS|\.\s*\*\s*-)', re.IGNORECASE)
        
        ignore_keywords_at_end =  ("ESI","HR","2D-NMR","2D NMR","LC-","LC","MALDI","EI","FT","MS","Fig.", "MS", "ATR", "DI", "FAB", "FT-", "signal", "FABMS", "HPm/z", "DIHRMS", "HPLC", "HRHRMS", ". * - ")
        for i, (item, index) in enumerate(positions):
            if skip_next:
                skip_next = False
                continue

            if item.lower() == "hrms" and i + 1 < len(positions):
                next_item, next_index = positions[i + 1]
                if next_item.lower() == "m/z":
                    combined_value = (
                        f"{data_text[index:next_index].strip()} "
                        f"{extract_finding_value(data_text, next_index, positions, i + 1).strip()}"
                    ).replace("NMR NMR", " NMR ").replace("HRMS", " ").strip()
                    combined_value = re.sub(r"\s*Optical Rotation\s*$", "", combined_value, flags=re.IGNORECASE).strip()
                    results["HRMS"] = results.get("HRMS", "") + f" | {combined_value}".strip(" |")
                    skip_next = True
                    continue

            value = extract_finding_value(data_text, index, positions, i).strip()
            value = value.replace("NMR NMR", " NMR ").replace("HR HRMS", "HRMS").replace("HRMS HRMS", "HRMS").replace("LRm/z (EI)", "m/z (EI)").strip()

            if not value or value.lower() == item.lower():
                continue

            if item == "[a]D":
                value_text = remove_space_between_aD_and_number(results.get(item, "") + value)
                results[item] = results.get(item, "") + f" | {value_text}" if item in results else value_text

            elif item == "Mp":
                value = extract_temperature_range(value)
                update_results(item, value, results)

            elif item == "EE":
                value = extract_percentage(value)
                update_results(item, value, results)

            elif item == "m/z":
                update_results(item, value, results)

            

            # elif item == "13C NMR":
            #     value_stripped = value
            #     if len(value_stripped) > 50:
            #         search_text = value_stripped[50:]
                    
            #         # Check for ppm first (preserve it)
            #         if "ppm" in search_text:
            #             ppm_match = ppm_pattern.search(search_text)
            #             if ppm_match:
            #                 # Include "ppm" in the result
            #                 value_stripped = value_stripped[:50 + ppm_match.end()].strip()
            #         # Check for other keywords (drop them)
            #         elif any(k in search_text for k in ignore_keywords_at_end):
            #             other_match = other_keywords_pattern.search(search_text)
            #             if other_match:
            #                 # Exclude the keyword from the result
            #                 value_stripped = value_stripped[:50 + other_match.end()].strip()
            #     # Remove any trailing opening parenthesis
            #     value_stripped = re.sub(r'\s*\($', '', value_stripped).strip()
            #     update_results(item, value_stripped, results)

            elif item == "13C NMR":
                value_stripped = value.strip()
                if len(value_stripped) > 50:
                    search_text = value_stripped[50:]
                    search_start_pos = 50
                    
                    # Check for ppm first (preserve it)
                    if "ppm" in search_text:
                        ppm_match = ppm_pattern.search(search_text)
                        if ppm_match:
                            # Include "ppm" in the result - use group(1) to get the captured part
                            match_end_pos = search_start_pos + ppm_match.end(1)
                            value_stripped = value_stripped[:match_end_pos].strip()
                            # Clean up end characters: remove trailing punctuation and opening parenthesis
                            value_stripped = re.sub(r'[,.;:!?(\s]+$', '', value_stripped)
                    # Check for other keywords (drop them)
                    elif any(k in search_text for k in ignore_keywords_at_end):
                        other_match = other_keywords_pattern.search(search_text)
                        if other_match:
                            # Exclude the keyword from the result
                            match_end_pos = search_start_pos + other_match.end()
                            value_stripped = value_stripped[:match_end_pos].strip()
                else:
                    # If text is 50 characters or less, search the entire text
                    search_text = value_stripped
                    # search_start_pos = 0
                    
                    # # Check for ppm first (preserve it)
                    # if "ppm" in search_text:
                    #     ppm_match = ppm_pattern.search(search_text)
                    #     if ppm_match:
                    #         # Include "ppm" in the result
                    #         value_stripped = value_stripped[:ppm_match.end(1)].strip()
                    #         # Clean up end characters: remove trailing punctuation and opening parenthesis
                    #         value_stripped = re.sub(r'[,.;:!?(\s]+$', '', value_stripped)
                    # Check for other keywords (drop them)
                    if any(k in search_text for k in ignore_keywords_at_end):
                        other_match = other_keywords_pattern.search(search_text)
                        if other_match:
                            # Exclude the keyword from the result
                            value_stripped = value_stripped[:other_match.end()].strip()
                
                # Remove any trailing opening parenthesis
                value_stripped = re.sub(r'\s*\($', '', value_stripped).strip()
                update_results(item, value_stripped, results)


            elif item.lower() == "19f nmr":
                
                value_stripped = value
                value_stripped = value_stripped.replace("19F NMR", "").replace("19F  NMR", "").strip()
                value = re.sub(r'^\s+|\s*\(\s*$|\s+$', '', value)
                # print(f"{value_stripped =}")
                update_results(item, value_stripped, results)

            elif item.lower() == "29si nmr":
                value_stripped = value
                value_stripped = value_stripped.replace("29SI NMR", "").replace("29Si NMR", "").strip()
                update_results(item, value_stripped, results)

            # elif item == "11B NMR":
                # value_stripped = value
                # if len(value_stripped) > 50:
                #     search_text = value_stripped[50:]
                    
                #     # Check for ppm first (preserve it)
                #     if "ppm" in search_text:
                #         ppm_match = ppm_pattern.search(search_text)
                #         if ppm_match:
                #             # Include "ppm" in the result
                #             value_stripped = value_stripped[:50 + ppm_match.end()].strip()
            elif item == "1H NMR":
                value_stripped = value.strip()
                if len(value_stripped) > 60:
                    search_text = value_stripped[60:]
                    search_start_pos = 60

                    # Check for ppm first (preserve it)
                    if "ppm" in search_text:
                        ppm_match = ppm_pattern.search(search_text)
                        if ppm_match:
                            # Include "ppm" in the result - use group(1) to get the captured part
                            match_end_pos = search_start_pos + ppm_match.end(1)
                            value_stripped = value_stripped[:match_end_pos].strip()
                            # Clean up end characters: remove trailing punctuation and opening parenthesis
                            value_stripped = re.sub(r'[,.;:!?(\s]+$', '', value_stripped)
                    # Check for other keywords (drop them)
                    elif any(k in search_text for k in ignore_keywords_at_end):
                        other_match = other_keywords_pattern.search(search_text)
                        if other_match:
                            # Exclude the keyword from the result
                            match_end_pos = search_start_pos + other_match.end()
                            value_stripped = value_stripped[:match_end_pos].strip()
                else:
                    # If text is 50 characters or less, search the entire text
                    search_text = value_stripped
                    search_start_pos = 0

                    # Check for other keywords (drop them)
                    if any(k in search_text for k in ignore_keywords_at_end):
                        other_match = other_keywords_pattern.search(search_text)
                        if other_match:
                            # Exclude the keyword from the result
                            value_stripped = value_stripped[:other_match.end()].strip()

                # Remove any trailing opening parenthesis
                value_stripped = re.sub(r'\s*\($', '', value_stripped).strip()
                update_results(item, value_stripped, results)

            # Handle other NMR types
            elif item in ["11B NMR", "15N NMR", "31P NMR", "2D NMR", "119Sn NMR"]:
                value_stripped = value.strip()
                # Remove the NMR type from the value
                value_stripped = value_stripped.replace(item, "").strip()
                # Apply similar length and keyword filtering as other NMR types
                if len(value_stripped) > 50:
                    search_text = value_stripped[50:]
                    if any(k in search_text for k in ignore_keywords_at_end):
                        other_match = other_keywords_pattern.search(search_text)
                        if other_match:
                            value_stripped = value_stripped[:50 + other_match.end()].strip()
                value_stripped = re.sub(r'\s*\($', '', value_stripped).strip()
                update_results(item, value_stripped, results)

            # Handle IR (Infrared Spectroscopy)
            elif item == "IR":
                value_stripped = value.strip()
                value_stripped = value_stripped.replace("IR", "").strip()
                # Look for wavenumber patterns (cm-1)
                ir_pattern = re.compile(r'.*\d+.*?\s*cm[-−]?1', re.IGNORECASE)
                ir_match = ir_pattern.search(value_stripped)
                if ir_match:
                    value_stripped = ir_match.group(0).strip()
                update_results(item, value_stripped, results)

            # Handle BP (Boiling Point)
            elif item == "BP":
                value_stripped = value.strip()
                value_stripped = value_stripped.replace("BP", "").replace("bp", "").strip()
                # Look for temperature patterns
                bp_pattern = re.compile(r'\b\d+(\.\d+)?(\s*-\s*\d+(\.\d+)?)?\s*°C\b', re.IGNORECASE)
                bp_match = bp_pattern.search(value_stripped)
                if bp_match:
                    value_stripped = bp_match.group(0).strip()
                update_results(item, value_stripped, results)

            # Handle DE (Diastereomeric Excess)
            elif item == "DE":
                value_stripped = value.strip()
                value_stripped = value_stripped.replace("DE", "").replace("de", "").strip()
                # Look for percentage patterns
                de_pattern = re.compile(r'[><=]?\s*\d+(?:\.\d+)?\s*%', re.IGNORECASE)
                de_match = de_pattern.search(value_stripped)
                if de_match:
                    value_stripped = de_match.group(0).strip()
                update_results(item, value_stripped, results)

            # Handle CRYSTAL DATA
            elif item == "CRYSTAL DATA":
                value_stripped = value.strip()
                value_stripped = value_stripped.replace("CRYSTAL DATA", "").strip()
                update_results(item, value_stripped, results)

            # Handle ELEMENTAL ANALYSIS
            elif item == "ELEMENTAL ANALYSIS":
                value_stripped = value.strip()
                value_stripped = value_stripped.replace("ELEMENTAL ANALYSIS", "").strip()
                update_results(item, value_stripped, results)

            # Handle Rf (Retention factor)
            elif item == "Rf":
                value_stripped = value.strip()
                value_stripped = value_stripped.replace("Rf", "").strip()
                # Look for Rf value patterns
                rf_pattern = re.compile(r'\b\d+\.\d+\b')
                rf_match = rf_pattern.search(value_stripped)
                if rf_match:
                    # Include some context around the Rf value
                    start_pos = max(0, rf_match.start() - 20)
                    end_pos = min(len(value_stripped), rf_match.end() + 50)
                    value_stripped = value_stripped[start_pos:end_pos].strip()
                update_results(item, value_stripped, results)

            # Handle Rt (Retention time)
            elif item == "Rt":
                value_stripped = value.strip()
                value_stripped = value_stripped.replace("Rt", "").replace("tR", "").strip()
                # Look for time patterns
                rt_pattern = re.compile(r'\b\d+(\.\d+)?\s*(min|minutes?|s|sec|seconds?)\b', re.IGNORECASE)
                rt_match = rt_pattern.search(value_stripped)
                if rt_match:
                    value_stripped = rt_match.group(0).strip()
                update_results(item, value_stripped, results)

            # Handle UV/vis
            elif item == "UV/vis":
                value_stripped = value.strip()
                value_stripped = value_stripped.replace("UV/vis", "").replace("UV-VIS", "").strip()
                # Look for wavelength patterns
                uv_pattern = re.compile(r'.*\d+.*?\s*nm', re.IGNORECASE)
                uv_match = uv_pattern.search(value_stripped)
                if uv_match:
                    value_stripped = uv_match.group(0).strip()
                update_results(item, value_stripped, results)

            # Handle raman
            elif item == "raman":
                value_stripped = value.strip()
                value_stripped = value_stripped.replace("raman", "").replace("Raman", "").strip()
                # Look for wavenumber patterns
                raman_pattern = re.compile(r'.*\d+.*?\s*cm[-−]?1', re.IGNORECASE)
                raman_match = raman_pattern.search(value_stripped)
                if raman_match:
                    value_stripped = raman_match.group(0).strip()
                update_results(item, value_stripped, results)

            # Handle HPLC
            elif item == "HPLC":
                value_stripped = value.strip()
                value_stripped = value_stripped.replace("HPLC", "").strip()
                update_results(item, value_stripped, results)

            else:
                # Remove any trailing opening parenthesis
                # value = re.sub(r'\s*\($', '', value).strip()
                value = re.sub(r'^\s+|\s*\(\s*$|\s+$', '', value)
                update_results(item, value, results)

        if ee_value is not None:
            results["EE"] = (results.get("EE", "") + f" | {ee_value}").strip(" |")

        df_findings = pd.DataFrame([{"FINDING_TYPE": k, "FINDING_VALUE_2": v.strip()} for k, v in results.items()])
        data_text, df_findings = process_findings(df_findings, data_text, ALL_FINDING_TYPES)
        data_text = data_text.replace("ENDOFDATATEXT", "").strip()

        rb_found_keys = df_findings["FINDING_TYPE"].unique().tolist()
        missing_data_keys = [key for key in ALL_FINDING_TYPES if key not in rb_found_keys]
        df_findings["rb_found_keys"] = [rb_found_keys] * len(df_findings)
        df_findings["missing_data_keys"] = [missing_data_keys] * len(df_findings)

        return data_text, df_findings

    except Exception as error:
        logging.error("get_finding_value Error: %s", str(error))
        return data_text, pd.DataFrame()



def update_results(item, value, results):
    """Update results dictionary by adding cleaned and valid values."""
    # Check if value is non-empty and not the same as the item name
    # if value and value.lower() != item.lower():
    if item in results:
        value = value.replace(item, " ").strip()
        results[item] += f" | {value}"
    else:
        value = value.replace(item, " ").strip()
        results[item] = value



def extract_temperature_range(data_text):
    """
    Extracts the temperature or range followed by '°C' from the given text.
    If multiple occurrences of '°C' are found, the function returns the original text.

    Parameters:
    - data_text (str): The input text to search.

    Returns:
    - str: The matched temperature string or the original text if multiple '°C' are found.
    """
    try:
        # Regex pattern to find all occurrences of a number or range followed by '°C'
        pattern = r'\b\d+(\.\d+)?(\s*-\s*\d+(\.\d+)?)?\s*°C\b'

        # Find all occurrences of the pattern
        matches = re.findall(pattern, data_text)

        if len(matches) > 1:
            # If multiple occurrences are found, return the original text
            data_text = data_text.replace("Mp .", "").replace("Mp.", "").replace("Mp:", "").replace("Mp :", "").replace("mp", "").replace("mp:", "").replace("Mp .", "").replace("Mp", "").replace("Mp", "").strip()
            return data_text

        elif matches:
            # If only one occurrence is found, return the first match with '°C' preserved
            final_value = re.search(pattern, data_text).group(0)
            final_value = final_value.replace("Mp .", "").replace("Mp.", "").replace("Mp:", "").replace("Mp :", "").replace("mp", "").replace("mp:", "").replace("Mp .", "").replace("Mp", "").replace("Mp", "").strip()
            return final_value

        else:
            # If no match is found, return data_text
            data_text = data_text.replace("Mp .", "").replace("Mp.", "").replace("Mp:", "").replace("Mp :", "").replace("mp", "").replace("mp:", "").replace("Mp .", "").replace("Mp", "").replace("Mp", "").strip()

            return data_text
    except Exception as error:
        # Log the error if any exception occurs
        logging.error("extract_temperature_range Error: %s", str(error))
        return data_text




def convert_rf_format(data_text):
    """
    Finds occurrences of 'Rf' followed by a number in the text (e.g., 'Rf0.45')
    and converts them to ' Rf 0.45 ' by adding spaces.

    Parameters:
    - data_text (str): The original text to be transformed.

    Returns:
    - str: The transformed text with 'Rf' patterns formatted correctly.
    """
    try:
        # Define the regex pattern to match 'Rf' followed directly by a number
        pattern = r"(?<=\bRf)(\d+\.\d+)"

        # Replace the matched pattern by adding spaces around 'Rf' and the number
        transformed_text = re.sub(pattern, r" \1 ", data_text)

        # Add a space between 'Rf' and the number, ensuring clean formatting
        transformed_text = re.sub(r"Rf\s*(\d+\.\d+)", r" Rf \1 ", transformed_text)

        # Clean up any extra spaces
        transformed_text = re.sub(r'\s+', ' ', transformed_text).strip()

        return transformed_text
    except Exception as error:
        # Log the error if any exception occurs
        logging.error("convert_rf_format Error: %s", str(error))
        return data_text



def process_findings(df_findings, data_text, finding_type):
    """
    Processes the findings DataFrame based on the given finding type.

    Parameters:
    - df_findings (pd.DataFrame): DataFrame containing findings.
    - data_text (str): The original data text.
    - finding_type (str or list): The type of finding to filter on.

    Returns:
    - Tuple: (Original data_text, Filtered or empty DataFrame)
    """

    # Ensure the DataFrame is not empty and concatenate finding values
    if not df_findings.empty:
        df_findings = concat_finding_values(df_findings)
        df_findings = remove_trailing_punctuation(df_findings, "FINDING_VALUE_2")
        df_findings = df_findings[~(df_findings["FINDING_TYPE"]== "ENDOFDATATEXT")]

    # If the DataFrame is empty after filtering, return the original data text and an empty DataFrame
    if df_findings.empty:
        return data_text, pd.DataFrame(columns=["FINDING_TYPE", "FINDING_VALUE_2", "rb_found_keys", "missing_data_keys"])

    # Return the original data text and the filtered DataFrame
    return data_text, df_findings



def remove_space_between_aD_and_number(text):
    """
    Removes spaces between '[a]D' and the following number in the input text.

    Parameters:
    - text (str): The original text to be processed.

    Returns:
    - str: The processed text with spaces removed between '[a]D' and the number.
    """
    # Use regex to find '[a]D' followed by spaces and then a number, and remove the spaces
    processed_text = re.sub(r"(\[a\]D)\s+(\d+)", r"\1\2", text)
    if processed_text:
        processed_text = processed_text.replace("[a]D [a]", " [a]").replace("[a]D[a]", " [a]").replace("  ", " ").strip()
        return processed_text
    else:
        return text



def find_item_positions(data_text, ALL_FINDING_TYPES):
    """
    Find the positions of all finding types in the input text.

    Parameters:
    - data_text (str): The text to search through for finding types
    - ALL_FINDING_TYPES (list): List of finding types to search for

    Returns:
    - list: List of tuples containing (finding_type, position) sorted by position
    """
    positions = []
    try:

        for item in ALL_FINDING_TYPES:
            # Use regex to find all occurrences of each finding type
            pattern = re.escape(item)
            matches = re.finditer(rf'(?<!\w){pattern}(?!\w)', data_text, re.IGNORECASE)

            for match in matches:
                positions.append((item, match.start()))
        return sorted(positions, key=lambda x: x[1])
    except Exception as error:
        # Log the error if any exception occurs
        logging.error("find_item_positions Error: %s", str(error))
        return positions


def extract_finding_value(data_text, index, positions, current_index):
    """
    Extract the finding value from the data text between the current position and the next finding type.

    Parameters:
    - data_text (str): The text to extract the finding value from
    - index (int): The starting position to extract from
    - positions (list): List of tuples containing (finding_type, position) sorted by position
    - current_index (int): The current index in the positions list

    Returns:
    - str: The extracted finding value text
    """

    try:
        start = index
        # Determine the end position based on the next finding type's index
        if current_index + 1 < len(positions):
            end = positions[current_index + 1][1]
        else:
            end = len(data_text)

        # Extract the substring between 'start' and 'end'
        value = data_text[start:end].strip()
        return value
    except Exception as error:
        # Log the error if any exception occurs
        logging.error("extract_finding_value Error: %s", str(error))
        return ""



def concat_finding_values(df):
    """
    Concatenate finding values for each finding type in the DataFrame.

    Parameters:
    - df (pandas.DataFrame): Input DataFrame containing 'FINDING_TYPE' and 'FINDING_VALUE_2' columns

    Returns:
    - pandas.DataFrame: Grouped DataFrame with concatenated finding values and preserved other columns
    """
    try:
        # Group by 'FINDING_TYPE' and concatenate 'FINDING_VALUE_2'
        df_grouped = (
            df.groupby('FINDING_TYPE', as_index=False)
            .agg({
                'FINDING_VALUE_2': lambda x: ' | '.join(map(str, x)),  # Concatenate values with separator
                **{col: 'first' for col in df.columns if col != 'FINDING_TYPE' and col != 'FINDING_VALUE_2'}
            })
        )
        return df_grouped
    except Exception as error:
        # Log the error if any exception occurs
        logging.error("concat_finding_values Error: %s", str(error))
        return df



def main_based_on_data_text(data_text, FINDING_TYPES, variable_excel_file, symbol_excel_file):
    """
    Process the input data text to extract findings based on specified finding types and excel files.

    Parameters:
    data_text (str): The input text data to be processed.
    FINDING_TYPES (list): A list of finding types to search for in the data.
    variable_excel_file (str): Path to the Excel file containing variable information.
    symbol_excel_file (str): Path to the Excel file containing symbol information.

    Returns:
    pandas.DataFrame: A DataFrame containing the processed findings with columns 'FINDING_TYPE' and 'FINDING_VALUE'.
    """
    try:
        # Store processed results
        results = []

        # Get the findings DataFrame
        data_text, findings_df = get_finding_value(data_text, FINDING_TYPES, variable_excel_file, symbol_excel_file)

        # print(f"{findings_df =}")

        data_text = data_text.replace("ENDOFDATATEXT", "").strip()

        # Handle findings_df if not empty
        if not findings_df.empty:
            for _, finding_row in findings_df.iterrows():
                finding_type_2 = finding_row.get('FINDING_TYPE', "")
                finding_value_2 = finding_row.get('FINDING_VALUE_2', "")
                finding_value_2 = str(finding_value_2)

                # Store the result with correct values
                results.append({
                    # "DATA_TEXT": data_text,
                    "FINDING_TYPE": finding_type_2,
                    "FINDING_VALUE": finding_value_2,
                })

        else:
            # Store default values if findings_df is empty
            results.append({
                # "DATA_TEXT": data_text,
                "FINDING_TYPE": "",
                "FINDING_VALUE": "",
            })

        # # Create a DataFrame from the results
        results_df = pd.DataFrame(results)

        # Save the results to an Excel file
        output_filename = rf'findings_output\processed_data_4.xlsx'
        results_df.to_excel(output_filename, index=False)
        print(f"Saved excel to {output_filename}.")

        return results_df
    except Exception as error:
        # Log the error if any exception occurs
        logging.error("main Error: %s", str(error))
        return pd.DataFrame()


def extract_all_finding_types_from_data_text(data_text, FINDING_TYPES, variable_excel_file, symbol_excel_file):
    """
    Process the input data text to extract ALL finding types and their values.
    For each finding type in FINDING_TYPES, return a row with FINDING_TYPE and FINDING_VALUE.
    If a finding type is not found, return empty string for FINDING_VALUE.

    Parameters:
    data_text (str): The input text data to be processed.
    FINDING_TYPES (list): A list of finding types to search for in the data.
    variable_excel_file (str): Path to the Excel file containing variable information.
    symbol_excel_file (str): Path to the Excel file containing symbol information.

    Returns:
    pandas.DataFrame: A DataFrame containing ALL finding types with columns 'FINDING_TYPE' and 'FINDING_VALUE'.
    """
    try:
        # Store processed results for all finding types
        all_results = []

        # Get the findings DataFrame using existing function
        processed_data_text, findings_df = get_finding_value(data_text, FINDING_TYPES, variable_excel_file, symbol_excel_file)

        # Create a dictionary of found findings for quick lookup
        found_findings = {}
        if not findings_df.empty:
            for _, finding_row in findings_df.iterrows():
                finding_type = finding_row.get('FINDING_TYPE', "")
                finding_value = finding_row.get('FINDING_VALUE_2', "")
                if finding_type and finding_type != "ENDOFDATATEXT":
                    found_findings[finding_type] = str(finding_value) if finding_value else ""

        # Process each finding type in the predefined list
        for finding_type in FINDING_TYPES:
            if finding_type == "ENDOFDATATEXT":
                continue  # Skip the end marker

            # Get the value if found, otherwise empty string
            finding_value = found_findings.get(finding_type, "")

            # Store the result
            all_results.append({
                "FINDING_TYPE": finding_type,
                "FINDING_VALUE": finding_value,
            })

        # Create a DataFrame from the results
        results_df = pd.DataFrame(all_results)

        return results_df
    except Exception as error:
        # Log the error if any exception occurs
        logging.error("extract_all_finding_types_from_data_text Error: %s", str(error))
        # Return DataFrame with all finding types and empty values in case of error
        error_results = [{"FINDING_TYPE": ft, "FINDING_VALUE": ""} for ft in FINDING_TYPES if ft != "ENDOFDATATEXT"]
        return pd.DataFrame(error_results)



def split_finding_value_rows(df):
    """
    Split rows where FINDING_VALUE contains "|" into separate rows.
    
    Parameters:
    - df (DataFrame): Input DataFrame with FINDING_VALUE column
    
    Returns:
    - DataFrame: New DataFrame with split rows
    """
    import pandas as pd
    
    new_rows = []
    
    for index, row in df.iterrows():
        # Handle different possible column names
        finding_value_col = None
        for col in ['FINDING_VALUE', 'FINDING_VALUE_2', 'finding_value']:
            if col in df.columns:
                finding_value_col = col
                break
        
        if finding_value_col is None:
            print("Warning: No FINDING_VALUE column found in DataFrame")
            print(f"Available columns: {list(df.columns)}")
            return df
        
        finding_value = str(row[finding_value_col])
        
        # Debug: Print the value being processed
        print(f"Processing row {index}: {finding_value[:100]}...")
        
        # Check if FINDING_VALUE contains "|"
        if '|' in finding_value:
            print(f"Found '|' in row {index}")
            # Split the FINDING_VALUE at "|"
            split_values = [value.strip() for value in finding_value.split('|')]
            print(f"Split into {len(split_values)} parts")
            
            # Create a new row for each split value
            for i, split_value in enumerate(split_values):
                if split_value:  # Only add non-empty values
                    new_row = row.copy()
                    new_row[finding_value_col] = split_value
                    new_rows.append(new_row)
                    print(f"Added split part {i+1}: {split_value[:50]}...")
        else:
            # print(f"No '|' found in row {index}")
            # Keep the original row if no "|" found
            new_rows.append(row)
    
    # Create new DataFrame from the list of rows
    result_df = pd.DataFrame(new_rows)
    
    # Reset index to have continuous numbering
    result_df.reset_index(drop=True, inplace=True)
    
    print(f"Original DataFrame shape: {df.shape}")
    print(f"Result DataFrame shape: {result_df.shape}")
    
    return result_df

def clean_right_end(text):
    """
    Strip specific characters and strings from the right end of text using regex.
    Handles patterns in order of priority.
    """
    if not isinstance(text, str):
        text = str(text)
    
    # Apply patterns in order - more specific first, then general
    patterns = [
        r'\.\s*\*\s*-\s*$',  # ". * - " with optional spaces
        r'~+$',              # One or more tildes at end
        r'\.+$',             # One or more periods at end  
        r'\*+$',             # One or more asterisks at end
        r'-+$',              # One or more hyphens at end
        r'\s+$'              # Trailing whitespace
    ]
    
    # Apply each pattern
    for pattern in patterns:
        text = re.sub(pattern, '', text)
    
    return text


def main_extract_all_findings(input_csv_file, FINDING_TYPES, variable_excel_file, symbol_excel_file, output_csv_file=None):
    """
    Process the input CSV file to extract ALL finding types for each row.
    For each row in the input CSV, generate rows for ALL finding types with their values.

    Parameters:
    input_csv_file (str): Path to the input CSV file containing DATA_TEXT column.
    FINDING_TYPES (list): A list of finding types to search for in the data.
    variable_excel_file (str): Path to the Excel file containing variable information.
    symbol_excel_file (str): Path to the Excel file containing symbol information.
    output_csv_file (str, optional): Path to save the output CSV file. If None, uses default naming.

    Returns:
    pandas.DataFrame: A DataFrame containing ALL findings with columns 'FINDING_TYPE' and 'FINDING_VALUE'.
    """
    try:
        # Read the input CSV file
        print(f"Loading data from {input_csv_file}...")
        input_df = pd.read_csv(input_csv_file)

        # Check if DATA_TEXT column exists (case-insensitive)
        data_text_col = None
        for col in input_df.columns:
            if col.upper() == 'DATA_TEXT':
                data_text_col = col
                break

        if data_text_col is None:
            raise ValueError("DATA_TEXT column not found in the input CSV file")

        print(f"Found DATA_TEXT column: {data_text_col}")
        print(f"Processing {len(input_df)} rows...")

        # Store processed results
        all_results = []

        # Process each row in the CSV
        for index, row in tqdm(input_df.iterrows()):
            if index % 100 == 0:
                print(f"Processing row {index + 1}/{len(input_df)}...")

            # Get the data_text from the current row
            data_text = str(row[data_text_col]) if pd.notna(row[data_text_col]) else ""

            # Skip empty data_text
            if not data_text.strip():
                print(f"Warning: Empty DATA_TEXT at row {index + 1}, skipping...")
                # Still create rows for all finding types with empty values
                for finding_type in FINDING_TYPES:
                    if finding_type == "ENDOFDATATEXT":
                        continue
                    result_row = row.to_dict()  # Copy all original columns
                    result_row.update({
                        "FINDING_TYPE": finding_type,
                        "FINDING_VALUE": "",
                        "ROW_INDEX": index + 1
                    })
                    all_results.append(result_row)
                continue

            # Extract all finding types for this row
            findings_df = extract_all_finding_types_from_data_text(
                data_text, FINDING_TYPES, variable_excel_file, symbol_excel_file
            )

            # Create result rows for all finding types
            for _, finding_row in findings_df.iterrows():
                finding_type = finding_row.get('FINDING_TYPE', "")
                finding_value = finding_row.get('FINDING_VALUE', "")

                # Create result row with original CSV data plus findings
                result_row = row.to_dict()  # Copy all original columns
                result_row.update({
                    "FINDING_TYPE": finding_type,
                    "FINDING_VALUE": finding_value,
                    "ROW_INDEX": index + 1
                })
                all_results.append(result_row)

        # Create DataFrame from all results
        results_df = pd.DataFrame(all_results)

        # Save to output file if specified
        if output_csv_file:
            print(f"Saving results to {output_csv_file}...")
            results_df.to_csv(output_csv_file, index=False)

            # Also save as Excel
            excel_file = output_csv_file.replace('.csv', '.xlsx')
            results_df.to_excel(excel_file, index=False)
            print(f"Results saved to {output_csv_file} and {excel_file}")

        print(f"Processing completed! Generated {len(results_df)} result rows.")
        return results_df

    except Exception as error:
        logging.error("main_extract_all_findings Error: %s", str(error))
        print(f"Error processing file: {error}")
        return pd.DataFrame()


def main(input_csv_file, FINDING_TYPES, variable_excel_file, symbol_excel_file, output_csv_file=None):
    """
    Process the input CSV file to extract findings based on specified finding types and excel files.

    Parameters:
    input_csv_file (str): Path to the input CSV file containing DATA_TEXT column.
    FINDING_TYPES (list): A list of finding types to search for in the data.
    variable_excel_file (str): Path to the Excel file containing variable information.
    symbol_excel_file (str): Path to the Excel file containing symbol information.
    output_csv_file (str, optional): Path to save the output CSV file. If None, uses default naming.

    Returns:
    pandas.DataFrame: A DataFrame containing the processed findings with columns 'FINDING_TYPE' and 'FINDING_VALUE'.
    """
    try:
        # Read the input CSV file
        print(f"Loading data from {input_csv_file}...")
        input_df = pd.read_csv(input_csv_file)
        # input_df = input_df.head(94)
        # Check if DATA_TEXT column exists (case-insensitive)
        data_text_col = None
        for col in input_df.columns:
            if col.upper() == 'DATA_TEXT':
                data_text_col = col
                break

        if data_text_col is None:
            raise ValueError("DATA_TEXT column not found in the input CSV file")

        print(f"Found DATA_TEXT column: {data_text_col}")
        print(f"Processing {len(input_df)} rows...")

        # Store processed results
        all_results = []

        # Process each row in the CSV
        for index, row in tqdm(input_df.iterrows()):
            if index % 100 == 0:
                print(f"Processing row {index + 1}/{len(input_df)}...")

            # Get the data_text from the current row
            data_text = str(row[data_text_col]) if pd.notna(row[data_text_col]) else ""

            # Skip empty data_text
            if not data_text.strip():
                print(f"Warning: Empty DATA_TEXT at row {index + 1}, skipping...")
                continue

            # Get the findings DataFrame for this row
            processed_data_text, findings_df = get_finding_value(
                data_text, FINDING_TYPES, variable_excel_file, symbol_excel_file
            )

            # Clean the processed data text
            processed_data_text = processed_data_text.replace("ENDOFDATATEXT", "").strip()

            # Handle findings_df if not empty
            if not findings_df.empty:
                for _, finding_row in findings_df.iterrows():
                    finding_type_2 = finding_row.get('FINDING_TYPE', "")
                    finding_value_2 = finding_row.get('FINDING_VALUE_2', "")
                    finding_value_2 = str(finding_value_2)

                    # Create result row with original CSV data plus findings
                    result_row = row.to_dict()  # Copy all original columns
                    result_row.update({
                        "PROCESSED_DATA_TEXT": processed_data_text,
                        "FINDING_TYPE": finding_type_2,
                        "FINDING_VALUE": finding_value_2,
                        "ROW_INDEX": index + 1
                    })
                    all_results.append(result_row)
            else:
                # Store default values if findings_df is empty
                result_row = row.to_dict()  # Copy all original columns
                result_row.update({
                    "PROCESSED_DATA_TEXT": processed_data_text,
                    "FINDING_TYPE": "",
                    "FINDING_VALUE": "",
                    "ROW_INDEX": index + 1
                })
                all_results.append(result_row)

        # Create a DataFrame from the results
        results_df = pd.DataFrame(all_results)

        # Generate output filename if not provided
        if output_csv_file is None:
            base_name = os.path.splitext(os.path.basename(input_csv_file))[0]
            output_csv_file = f'findings_output/{base_name}_processed_findings.csv'

        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(output_csv_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # Save the results to CSV file
        results_df.to_csv(output_csv_file, index=False)
        print(f"Results saved to {output_csv_file}")

        # Also save to Excel for compatibility
        excel_output = output_csv_file.replace('.csv', '.xlsx')
        results_df.to_excel(excel_output, index=False)
        print(f"Results also saved to {excel_output}")

        # Print summary statistics
        print(f"\nProcessing Summary:")
        print(f"Total input rows: {len(input_df)}")
        print(f"Total output rows: {len(results_df)}")
        print(f"Rows with findings: {len(results_df[results_df['FINDING_TYPE'] != ''])}")
        print(f"Rows without findings: {len(results_df[results_df['FINDING_TYPE'] == ''])}")

        # Show breakdown by finding type
        if len(results_df[results_df['FINDING_TYPE'] != '']) > 0:
            print("\nFindings by type:")
            finding_summary = results_df[results_df['FINDING_TYPE'] != ''].groupby('FINDING_TYPE').size().sort_values(ascending=False)
            for finding_type, count in finding_summary.items():
                print(f"  {finding_type}: {count}")

        # Splitting finding_value into multiple rows  at "|"
        results_df = split_finding_value_rows(results_df)
        results_df['FINDING_VALUE'] = results_df['FINDING_VALUE'].apply(clean_right_end)
        return results_df

    except Exception as error:
        # Log the error if any exception occurs
        logging.error("main Error: %s", str(error))
        print(f"Error in main function: {str(error)}")
        return pd.DataFrame()



def combine_input_output_csvs(input_csv, output_csv, combined_csv):
    """
    Combine input and output CSV files based on common columns:
    TAN_NAME, RXN_NUM, DATA_TEXT, FINDING_TYPE
    Clean both DATA_TEXT and FINDING_VALUE from input
    """
    print("Loading input and output CSV files...")

    # Load both CSV files
    input_df = pd.read_csv(input_csv)
    # input_df = input_df.head(100)
    output_df = pd.read_csv(output_csv)

    print(f"Input CSV rows: {len(input_df)}")
    print(f"Output CSV rows: {len(output_df)}")

    # Check for duplicate columns and remove them
    print("Checking for duplicate columns...")

    # Remove duplicate columns from input_df
    input_df = input_df.loc[:, ~input_df.columns.duplicated()]
    output_df = output_df.loc[:, ~output_df.columns.duplicated()]

    # Check available columns in input
    print(f"Input columns (after removing duplicates): {list(input_df.columns)}")
    print(f"Output columns (after removing duplicates): {list(output_df.columns)}")

    # Find the correct column names (case-insensitive)
    def find_column(df, target_col):
        for col in df.columns:
            if target_col.lower() in col.lower():
                return col
        return None

    # Map column names for input dataframe
    input_col_mapping = {}
    for target in ['TAN_NAME', 'RXN_NUM', 'DATA_TEXT', 'FINDING_TYPE', 'FINDING_VALUE']:
        found_col = find_column(input_df, target)
        if found_col:
            input_col_mapping[target] = found_col
        else:
            print(f"Warning: {target} column not found in input CSV")

    # Clean and standardize input dataframe
    input_clean = input_df.copy()

    # Rename columns to standard names
    for standard_name, actual_name in input_col_mapping.items():
        if actual_name != standard_name and actual_name in input_clean.columns:
            input_clean = input_clean.rename(columns={actual_name: standard_name})

    # Define the exact columns we want in the final output
    required_columns = ['TAN_NAME', 'RXN_NUM', 'DATA_TEXT', 'FINDING_TYPE', 'FINDING_VALUE']

    # Ensure all required columns exist in both dataframes
    for col in required_columns:
        if col not in input_clean.columns:
            input_clean[col] = ''
            print(f"Added missing column '{col}' to input dataframe")
        if col not in output_df.columns:
            output_df[col] = ''
            print(f"Added missing column '{col}' to output dataframe")

    # Handle duplicate column names between input and output
    # Rename output columns if they conflict with input columns (except required ones)
    output_df_renamed = output_df.copy()
    for col in output_df.columns:
        if col in input_clean.columns and col not in required_columns and col != 'SOURCE':
            new_col_name = f"{col}_OUTPUT"
            output_df_renamed = output_df_renamed.rename(columns={col: new_col_name})
            print(f"Renamed duplicate column '{col}' to '{new_col_name}' in output dataframe")

    # Add source column to identify origin
    input_clean['SOURCE'] = 'INPUT'
    output_df_renamed['SOURCE'] = 'OUTPUT'

    # Select only the required columns plus source
    final_columns = required_columns + ['SOURCE']

    # Create clean subsets with only the required columns
    try:
        input_subset = input_clean[final_columns].copy()
        output_subset = output_df_renamed[final_columns].copy()

        # Reset index to avoid any index issues
        input_subset = input_subset.reset_index(drop=True)
        output_subset = output_subset.reset_index(drop=True)

        print(f"Input subset shape: {input_subset.shape}")
        print(f"Output subset shape: {output_subset.shape}")
        # print(f"Input subset columns: {list(input_subset.columns)}")
        # print(f"Output subset columns: {list(output_subset.columns)}")

        # Verify no duplicate columns exist
        if input_subset.columns.duplicated().any():
            print("Warning: Duplicate columns found in input subset")
            input_subset = input_subset.loc[:, ~input_subset.columns.duplicated()]

        if output_subset.columns.duplicated().any():
            print("Warning: Duplicate columns found in output subset")
            output_subset = output_subset.loc[:, ~output_subset.columns.duplicated()]

        # Combine the dataframes
        combined_df = pd.concat([input_subset, output_subset], ignore_index=True)

    except Exception as e:
        print(f"Error during concatenation: {e}")
        print("Attempting alternative approach...")

        # Alternative approach: create new dataframes with explicit column order
        input_data = []
        for _, row in input_clean.iterrows():
            input_data.append({
                'TAN_NAME': row.get('TAN_NAME', ''),
                'RXN_NUM': row.get('RXN_NUM', ''),
                'DATA_TEXT': row.get('DATA_TEXT', ''),
                'FINDING_TYPE': row.get('FINDING_TYPE', ''),
                'FINDING_VALUE': row.get('FINDING_VALUE', ''),
                'SOURCE': 'INPUT'
            })

        output_data = []
        for _, row in output_df_renamed.iterrows():
            output_data.append({
                'TAN_NAME': row.get('TAN_NAME', ''),
                'RXN_NUM': row.get('RXN_NUM', ''),
                'DATA_TEXT': row.get('DATA_TEXT', ''),
                'FINDING_TYPE': row.get('FINDING_TYPE', ''),
                'FINDING_VALUE': row.get('FINDING_VALUE', ''),
                'SOURCE': 'OUTPUT'
            })

        # Create new dataframes
        input_subset = pd.DataFrame(input_data)
        output_subset = pd.DataFrame(output_data)

        # Combine
        combined_df = pd.concat([input_subset, output_subset], ignore_index=True)

    # Sort by TAN_NAME, RXN_NUM, FINDING_TYPE for better organization
    sort_columns = []
    if 'TAN_NAME' in combined_df.columns and not combined_df['TAN_NAME'].isna().all():
        sort_columns.append('TAN_NAME')
    if 'RXN_NUM' in combined_df.columns and not combined_df['RXN_NUM'].isna().all():
        sort_columns.append('RXN_NUM')
    if 'FINDING_TYPE' in combined_df.columns and not combined_df['FINDING_TYPE'].isna().all():
        sort_columns.append('FINDING_TYPE')

    if sort_columns:
        try:
            combined_df = combined_df.sort_values(sort_columns).reset_index(drop=True)
        except Exception as e:
            print(f"Warning: Could not sort dataframe: {e}")

    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(combined_csv)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Save combined CSV
    combined_df.to_csv(combined_csv, index=False)

    print(f"\nCombination complete!")
    print(f"Combined CSV rows: {len(combined_df)}")
    print(f"Input rows in combined: {len(combined_df[combined_df['SOURCE'] == 'INPUT'])}")
    print(f"Output rows in combined: {len(combined_df[combined_df['SOURCE'] == 'OUTPUT'])}")
    print(f"Combined CSV saved to: {combined_csv}")

    # Show sample of combined data
    print(f"\nSample of combined data (first 5 rows):")
    print(combined_df.head())

    return combined_df


def process_data_with_combination(input_csv, output_csv, combined_csv) : #, FINDING_TYPES, variable_excel_file, symbol_excel_file):
    """
    Complete pipeline: process data and then combine input/output
    """
    print("▶ Step 1: Processing data to extract findings...")

    # Process the input CSV to generate output CSV
    # results_df = main(input_csv, FINDING_TYPES, variable_excel_file, symbol_excel_file, output_csv)

    print("▶ Step 2: Combining input and output CSVs...")
    combined_df = combine_input_output_csvs(input_csv, output_csv, combined_csv)

    return combined_df


def split_finding_value_rows(df):
    """
    Split rows where FINDING_VALUE contains "|" into separate rows.
    
    Parameters:
    - df (DataFrame): Input DataFrame with FINDING_VALUE column
    
    Returns:
    - DataFrame: New DataFrame with split rows
    """
    
    
    new_rows = []
    
    for index, row in df.iterrows():
        # Handle different possible column names
        finding_value_col = None
        for col in ['FINDING_VALUE', 'FINDING_VALUE_2', 'finding_value']:
            if col in df.columns:
                finding_value_col = col
                break
        
        if finding_value_col is None:
            print("Warning: No FINDING_VALUE column found in DataFrame")
            print(f"Available columns: {list(df.columns)}")
            return df
        
        finding_value = str(row[finding_value_col])
        
        # Debug: Print the value being processed
        print(f"Processing row {index}: {finding_value[:100]}...")
        
        # Check if FINDING_VALUE contains "|"
        if '|' in finding_value:
            print(f"Found '|' in row {index}")
            # Split the FINDING_VALUE at "|"
            split_values = [value.strip() for value in finding_value.split('|')]
            print(f"Split into {len(split_values)} parts")
            
            # Create a new row for each split value
            for i, split_value in enumerate(split_values):
                if split_value:  # Only add non-empty values
                    new_row = row.copy()
                    new_row[finding_value_col] = split_value
                    new_rows.append(new_row)
                    print(f"Added split part {i+1}: {split_value[:50]}...")
        else:
            # print(f"No '|' found in row {index}")
            # Keep the original row if no "|" found
            new_rows.append(row)
    
    # Create new DataFrame from the list of rows
    result_df = pd.DataFrame(new_rows)
    
    # Reset index to have continuous numbering
    result_df.reset_index(drop=True, inplace=True)
    
    print(f"Original DataFrame shape: {df.shape}")
    print(f"Result DataFrame shape: {result_df.shape}")
    
    return result_df
def compare_finding_value_lengths(input_csv_path, output_csv_path):
    # Load CSV
    df = pd.read_csv(input_csv_path)

    # Drop exact duplicate rows
    df = df.drop_duplicates()

    # Add new column with length of FINDING_VALUE
    df['VALUE_LENGTH'] = df['FINDING_VALUE'].astype(str).str.len()

    # Pivot to have INPUT and OUTPUT lengths in same row
    pivot_df = df.pivot_table(
        index=['TAN_NAME', 'RXN_NUM', 'FINDING_TYPE'],
        columns='SOURCE',
        values='VALUE_LENGTH',
        aggfunc='first'  # Assuming 1 input and 1 output per group
    ).reset_index()

    # Calculate length difference
    pivot_df['LENGTH_DIFF'] = pivot_df.get('OUTPUT', 0) - pivot_df.get('INPUT', 0)

    # Merge back with original data (optional, to preserve full context)
    merged = pd.merge(df, pivot_df, on=['TAN_NAME', 'RXN_NUM', 'FINDING_TYPE'], how='left')
    merged.drop_duplicates(inplace=True)
    merged = split_finding_value_rows(merged)
    # Save result
    merged.to_csv(output_csv_path, index=False)
    return merged





def test_single_data_text(data_text, FINDING_TYPES, variable_excel_file, symbol_excel_file):
    """
    Test function to extract findings from a single DATA_TEXT and display results.

    Parameters:
    data_text (str): The input text data to be processed.
    FINDING_TYPES (list): A list of finding types to search for in the data.
    variable_excel_file (str): Path to the Excel file containing variable information.
    symbol_excel_file (str): Path to the Excel file containing symbol information.

    Returns:
    pandas.DataFrame: A DataFrame containing ALL finding types and their values.
    """
    print("Testing single DATA_TEXT extraction...")
    print(f"Input DATA_TEXT: {data_text[:200]}...")

    # Extract all finding types
    results_df = extract_all_finding_types_from_data_text(
        data_text, FINDING_TYPES, variable_excel_file, symbol_excel_file
    )

    print(f"\nResults:")
    print(f"Total finding types: {len(results_df)}")
    print(f"Finding types with values: {len(results_df[results_df['FINDING_VALUE'].str.strip() != ''])}")

    # Display all results
    for _, row in results_df.iterrows():
        finding_type = row['FINDING_TYPE']
        finding_value = row['FINDING_VALUE']
        status = "✓" if finding_value.strip() else "✗"
        print(f"{status} {finding_type}: {finding_value}")

    return results_df


def demo_functionality():
    """
    Demonstrate the functionality with sample data.
    """
    print("=" * 80)
    print("DEMONSTRATION: Reaction Findings Extraction")
    print("=" * 80)

    # Sample DATA_TEXT from the cleaned CSV
    sample_data_text = """Rf 0.71 (2:1 tol uene/EtOAc), [a]D +15° (c 0.58, CHCl3),11H NMR δH (600 MHz, CDCl3): 8.02 (d, 2H, J = 7.5 Hz, Ar-H), 7.69 (d, 2H, J = 8.1 Hz, Ar-H), 7.66 (d, 4H, J = 7.9 Hz, Ar-H), 7.52 (d, 2H, J = 8.0 Hz, Ar-H), 7.49 (t, 1H, J = 7.4 Hz, Ar-H), 7.40 (t, 1H, J = 7.4 Hz, Ar-H), 7.28 (t, 2H, J = 7.7 Hz, Ar-H), 7.24 (t, 2H, J = 7.6 Hz, Ar-H), 7.15 (d, 2H, J = 8.1 Hz, Ar-H), 7.00 (t, 4H, J = 7.4 Hz, Ar-H), 6.93 (d, 2H, J = 9.1 Hz, Ar-H), 6.74 (d, 2H, J = 9.1 Hz, Ar-H), 5.93 (brt, 1H, J = 9.9 Hz, Xyl-3), 5.68 (brt, 1H, J = 7.8 Hz, GlcA-2), 5.68 (brt, 1H, J = 8.8 Hz, GlcA-4), 5.54 (d, 1H, J1,2 = 3.8 Hz, Xyl-1), 5.22 (d, 1H, J1,2 = 7.1 Hz, GlcA-1), 5.13 (m, 2H, Xyl-2,4), 4.53 (brt, 1H, J = 8.6 Hz, GlcA-3), 4.24 (d, 1H, J4,5 = 9.0 Hz, GlcA-5), 3.77 (brt, 1H, J = 11.0 Hz, Xyl-5a), 3.71 (s, 3H, Ar-OCH3), 3.65 (dd, 1H, J4,5e = 5.8 Hz, J5a/5e = 11.2 Hz, Xyl-5e), 3.52 (s, 3H, COOCH3), 2.37, 2.31, 2.24 (3s, each 3H, Ar-CH3),13C NMR δC (CDCl3): 167.29 (GlcA-6), 165.61, 165.47, 165.33, 165.05, 164.65 (Ar-C- -O), 155.80, 151.05, 144.02, 143.72, 143.58, 133.36, 133.24, 129.91, 129.68, 129.62, 129.60, 129.38, 129.03, 128.87, 128.82, 128.60, 128.45, 128.27, 126.59, 126.21, 125.65, 119.11, 114.49 (Ar), 100.69 (GlcA-1), 97.63 (Xyl-1), 77.22 (GlcA-3), 72.81 (GlcA-4), 72.33 (GlcA-5), 71.67 (GlcA-2), 70.85 (Xyl-2), 69.74 (Xyl-4), 69.34 (Xyl-3), 59.35 (Xyl-5), 55.59 (Ar-OCH3), 52.74 (COOCH3), 21.69, 21.68, 21.52 (Ar-CH3), HRMS m/z MS ESI: m/z calcd for C57H52NaO17 [M+Na]+,1031.3097; found, 1031.3093."""

    FINDING_TYPES = [
        "11B NMR", "13C NMR", "15N NMR", "19F NMR", "1H NMR", "29Si NMR",
        "31P NMR", "BP", "CRYSTAL DATA", "DE", "EE", "ELEMENTAL ANALYSIS",
        "HRMS", "IR", "m/z", "Mp", "Rf", "Rt", "UV/vis", "[a]D",
        "raman","2D NMR","119Sn NMR","HPLC",  "ENDOFDATATEXT"
    ]

    variable_excel_file = 'variable_cleaning_2.xlsx'
    symbol_excel_file = "variable_cleaning_symbols_2.xlsx"

    # Test the extraction
    results_df = test_single_data_text(sample_data_text, FINDING_TYPES, variable_excel_file, symbol_excel_file)

    print("\n" + "=" * 80)
    print("DEMONSTRATION COMPLETED")
    print("=" * 80)

    return results_df

#######################################################
if __name__ == "__main__":

    start_time = time.time()
    # Initialize logger
    logging.basicConfig(filename='reaction_finding_by_rules.log', level=logging.ERROR, format='%(asctime)s - %(levelname)s: %(message)s')

    FINDING_TYPES = [
        "11B NMR", "13C NMR", "15N NMR", "19F NMR", "1H NMR", "29Si NMR",
        "31P NMR", "BP", "CRYSTAL DATA", "DE", "EE", "ELEMENTAL ANALYSIS",
        "HRMS", "IR", "m/z", "Mp", "Rf", "Rt", "UV/vis", "[a]D",
        "raman","2D NMR","119Sn NMR","HPLC",  "ENDOFDATATEXT"
    ]

    variable_excel_file = 'variable_cleaning_2.xlsx'
    symbol_excel_file = "variable_cleaning_symbols_2.xlsx"

    # Input CSV file path
    # input_csv_file = "rxnfile.982508_AJ.csv"
    input_csv_file = "UII_Shipment_982279_TAN_RxnFindings_20250623_cleaned.csv"

    # output CSV file path
    output_csv_file = "findings_output/processed_findings_results.csv"
    combined_csv = f"findings_output/{input_csv_file.replace('.csv', '_combined.csv')}"


    # Process the CSV file using the new function that extracts ALL finding types
    print("Using main_extract_all_findings to extract ALL finding types for each row...")
    results = main_extract_all_findings(input_csv_file, FINDING_TYPES, variable_excel_file, symbol_excel_file, output_csv_file)

    print(f"\nProcessing completed!")
    print(f"Results shape: {results.shape}")
    print(f"Time required = {round((time.time() - start_time), 2)} seconds")

    # Display summary statistics
    if not results.empty:
        print(f"\nSummary:")
        print(f"Total rows processed: {len(results)}")
        print(f"Unique finding types: {results['FINDING_TYPE'].nunique()}")
        print(f"Rows with non-empty finding values: {len(results[results['FINDING_VALUE'].str.strip() != ''])}")

        print(f"\nFinding types distribution:")
        finding_counts = results['FINDING_TYPE'].value_counts()
        print(finding_counts)

        print(f"\nFirst 10 results:")
        print(results[['FINDING_TYPE', 'FINDING_VALUE']].head(10).to_string())

    # Option 1: Run complete pipeline
    process_data_with_combination(input_csv_file, output_csv_file, combined_csv)

    # Option 2: If you already have both CSVs, just combine them
    combine_input_output_csvs(input_csv_file, output_csv_file, combined_csv)

    input_csv = r"C:\Users\<USER>\Desktop\Reaction_findings_2025\findings_output\UII_Shipment_982279_TAN_RxnFindings_20250623_cleaned_combined.csv"
    output_csv = input_csv.replace(".csv", "_analysis_v7.csv")

    compare_finding_value_lengths(input_csv, output_csv)

####################################################################

# if __name__ == "__main__":
#     start_time = time.time()

#     # Define file paths
#     input_csv_file = 'UII_Shipment_982279_TAN_RxnFindings_20250623_cleaned.csv'
#     output_csv_file = 'UII_Shipment_982279_TAN_RxnFindings_20250623_output_all_findings.csv'
#     combined_csv = 'UII_Shipment_982279_TAN_RxnFindings_20250623_combined.csv'
#     variable_excel_file = 'variable_cleaning_2.xlsx'
#     symbol_excel_file = "variable_cleaning_symbols_2.xlsx"

#     # Define finding types
#     FINDING_TYPES = [
#         "11B NMR", "13C NMR", "15N NMR", "19F NMR", "1H NMR", "29Si NMR",
#         "31P NMR", "BP", "CRYSTAL DATA", "DE", "EE", "ELEMENTAL ANALYSIS",
#         "HRMS", "IR", "m/z", "Mp", "Rf", "Rt", "UV/vis", "[a]D",
#         "raman","2D NMR","119Sn NMR","HPLC",  "ENDOFDATATEXT"
#     ]

#     print("Starting reaction findings extraction...")
#     print(f"Input file: {input_csv_file}")
#     print(f"Output file: {output_csv_file}")
#     print(f"Finding types: {len(FINDING_TYPES)} types")

#     # Option to run demo first
#     print("\n" + "="*50)
#     print("DEMO: Testing with sample data...")
#     print("="*50)
#     demo_results = demo_functionality()

#     print("\n" + "="*50)
#     print("PROCESSING FULL CSV FILE...")
#     print("="*50)

    # # Process the CSV file using the new function that extracts ALL finding types
    # print("Using main_extract_all_findings to extract ALL finding types for each row...")
    # results = main_extract_all_findings(input_csv_file, FINDING_TYPES, variable_excel_file, symbol_excel_file, output_csv_file)

    # print(f"\nProcessing completed!")
    # print(f"Results shape: {results.shape}")
    # print(f"Time required = {round((time.time() - start_time), 2)} seconds")

    # # Display summary statistics
    # if not results.empty:
    #     print(f"\nSummary:")
    #     print(f"Total rows processed: {len(results)}")
    #     print(f"Unique finding types: {results['FINDING_TYPE'].nunique()}")
    #     print(f"Rows with non-empty finding values: {len(results[results['FINDING_VALUE'].str.strip() != ''])}")

    #     print(f"\nFinding types distribution:")
    #     finding_counts = results['FINDING_TYPE'].value_counts()
    #     print(finding_counts)

    #     print(f"\nFirst 10 results:")
    #     print(results[['FINDING_TYPE', 'FINDING_VALUE']].head(10).to_string())
