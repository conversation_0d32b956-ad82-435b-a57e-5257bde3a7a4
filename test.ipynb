import re


def update_results(item, value, results):
    """Update results dictionary by adding cleaned and valid values."""
    # Check if value is non-empty and not the same as the item name
    # if value and value.lower() != item.lower():
    if item in results:
        value = value.replace(item, " ").strip()
        results[item] += f" | {value}"
    else:
        value = value.replace(item, " ").strip()
        results[item] = value


item = "13c nmr"
value = '13C NMR δC (CDCl3): 167.29 (GlcA-6), 165.61, 165.47, ppm. any unnecessary data'
value = '13C NMR δC (CDCl3): 167.29 (GlcA-6), 165.61, 165.47'

results = {}
if item.lower() == "13c nmr":
    # print(f"{item = }")
    value_stripped = value.strip()
    # print(f"{value_stripped = }")
    if len(value_stripped) !=0:                     
        # Look for ppm pattern after first 50 characters
        search_text = value_stripped[50:] if len(value_stripped) > 50 else value_stripped
        print(f"{search_text = }")
        # Regex to find float values (with optional spaces) followed by ppm
        # Pattern: one or more groups of (optional spaces, float, optional comma/space) followed by ppm
        ppm_pattern = r'(?:\s*\d+(?:\.\d+)?\s*,?\s*)+ppm[^\w]*'
        
        ppm_match = re.search(ppm_pattern, search_text, re.IGNORECASE)
        print(f"{ppm_match = }")
        if ppm_match:
            print(f"{ppm_match.group() = }")
            # Calculate the actual position in the original string
            if len(value_stripped) > 50:
                match_end_pos = 50 + ppm_match.end()
            else:
                match_end_pos = ppm_match.end()
            
            # Extract text up to the end of the matched pattern
            value_stripped = value_stripped[:match_end_pos].strip()
        
        # Always update results - either with trimmed text (if ppm found) or whole text (if ppm not found)
        update_results(item, value_stripped, results)
print(results)

import re

def update_results(item, value, results):
    """Update results dictionary by adding cleaned and valid values."""
    value = value.replace(item, " ").strip()
    if item in results:
        results[item] += f" | {value}"
    else:
        results[item] = value

def process_value(item, value, results):
    # item_lower = item.lower()
    

    # if not value_stripped:
    #     return  # Skip empty values
    if item.lower() == "13c nmr":
        value_stripped = value.strip()
        if value_stripped:
            # Check for 'ppm' after first 50 characters (if applicable)
            search_text = value_stripped[50:] if len(value_stripped) > 50 else value_stripped

            ppm_pattern = r'(?:\s*\d+(?:\.\d+)?\s*,?\s*)+ppm[^\w]*'
            ppm_match = re.search(ppm_pattern, search_text, re.IGNORECASE)

            if ppm_match:
                print("ppm match found")
                # End position of the match relative to original text
                match_end_pos = (50 + ppm_match.end()) if len(value_stripped) > 50 else ppm_match.end()
                value_stripped = value_stripped[:match_end_pos].strip()

            update_results(item, value_stripped, results)

# Sample test
item = "13C NMR"

# Case 1: Unwanted extra text after 'ppm'
value1 = '13C NMR δC (CDCl3): 167.29 (GlcA-6), 165.61, 165.47, ppm. any unnecessary data'
# Case 2: Ends naturally, no noise
value2 = 'Rf (DCM : MeOH : NH4OH = 9 : 1 : 0.1) = 0.06; 11H NMR (400 MHz, DMSO-d6)δ 11.26 (s, 2H), 8.34 (s, 1H), 7.47 (s, 1H), 7.41-7.17 (m, 5H), 6.18 (s, 1H), 5.00-4.57 (m, 2H), 3.47-3.37 (m, 4H), 3.08-2.82 (m, 3H), 2.79-2.72 (m, 4H) ppm; 13C NMR (101 MHz, DMSO-d6)δ 164.4, 162.1, 160.8, 158.7, 157.3, 156.6, 144.7, 137.6, 137.4, 128.6, 127.6, 127.4, 116.7, 116.1, 84.8, 53.3, 50.3, 46.3, 46.1, 45.1, 44.6, 40.6, 36.3, 33.2 ppm, rotamers are present in the spectrum; HRHRMS HRMS MS(ESI+) calcd for C20H24ON7S m/z [M + H]+: 410.17576, found: 410.17554; UHPLC: tr: 2.89 min (100% at 254 nm).'

results = {}

# process_value(item, value1, results)
process_value(item, value2, results)

# Output
print(results)


import pandas as pd
def split_finding_value_rows(df):
    """
    Split rows where FINDING_VALUE contains "|" into separate rows.
    
    Parameters:
    - df (DataFrame): Input DataFrame with FINDING_VALUE column
    
    Returns:
    - DataFrame: New DataFrame with split rows
    """
    import pandas as pd
    
    new_rows = []
    
    for index, row in df.iterrows():
        # Handle different possible column names
        finding_value_col = None
        for col in ['FINDING_VALUE', 'FINDING_VALUE_2', 'finding_value']:
            if col in df.columns:
                finding_value_col = col
                break
        
        if finding_value_col is None:
            print("Warning: No FINDING_VALUE column found in DataFrame")
            print(f"Available columns: {list(df.columns)}")
            return df
        
        finding_value = str(row[finding_value_col])
        
        # Debug: Print the value being processed
        print(f"Processing row {index}: {finding_value[:100]}...")
        
        # Check if FINDING_VALUE contains "|"
        if '|' in finding_value:
            print(f"Found '|' in row {index}")
            # Split the FINDING_VALUE at "|"
            split_values = [value.strip() for value in finding_value.split('|')]
            print(f"Split into {len(split_values)} parts")
            
            # Create a new row for each split value
            for i, split_value in enumerate(split_values):
                if split_value:  # Only add non-empty values
                    new_row = row.copy()
                    new_row[finding_value_col] = split_value
                    new_rows.append(new_row)
                    print(f"Added split part {i+1}: {split_value[:50]}...")
        else:
            # print(f"No '|' found in row {index}")
            # Keep the original row if no "|" found
            new_rows.append(row)
    
    # Create new DataFrame from the list of rows
    result_df = pd.DataFrame(new_rows)
    
    # Reset index to have continuous numbering
    result_df.reset_index(drop=True, inplace=True)
    
    print(f"Original DataFrame shape: {df.shape}")
    print(f"Result DataFrame shape: {result_df.shape}")
    
    return result_df
def compare_finding_value_lengths(input_csv_path, output_csv_path):
    # Load CSV
    df = pd.read_csv(input_csv_path)

    # Drop exact duplicate rows
    df = df.drop_duplicates()

    # Add new column with length of FINDING_VALUE
    df['VALUE_LENGTH'] = df['FINDING_VALUE'].astype(str).str.len()

    # Pivot to have INPUT and OUTPUT lengths in same row
    pivot_df = df.pivot_table(
        index=['TAN_NAME', 'RXN_NUM', 'FINDING_TYPE'],
        columns='SOURCE',
        values='VALUE_LENGTH',
        aggfunc='first'  # Assuming 1 input and 1 output per group
    ).reset_index()

    # Calculate length difference
    pivot_df['LENGTH_DIFF'] = pivot_df.get('OUTPUT', 0) - pivot_df.get('INPUT', 0)

    # Merge back with original data (optional, to preserve full context)
    merged = pd.merge(df, pivot_df, on=['TAN_NAME', 'RXN_NUM', 'FINDING_TYPE'], how='left')
    merged.drop_duplicates(inplace=True)
    merged = split_finding_value_rows(merged)
    # Save result
    merged.to_csv(output_csv_path, index=False)
    return merged

input_csv = r"C:\Users\<USER>\Desktop\Reaction_findings_2025\findings_output\UII_Shipment_982279_TAN_RxnFindings_20250623_cleaned_combined.csv"
output_csv = input_csv.replace(".csv", "_analysis_v6.csv")

compare_finding_value_lengths(input_csv, output_csv)


def clean_right_end(text):
    """
    Strip specific characters and strings from the right end of text using regex.
    Handles patterns in order of priority.
    """
    import re
    
    if not isinstance(text, str):
        text = str(text)
    
    # Apply patterns in order - more specific first, then general
    patterns = [
        r'\.\s*\*\s*-\s*$',  # ". * - " with optional spaces
        r'~+$',              # One or more tildes at end
        r'\.+$',             # One or more periods at end  
        r'\*+$',             # One or more asterisks at end
        r'-+$',              # One or more hyphens at end
        r'\s+$'              # Trailing whitespace
    ]
    
    # Apply each pattern
    for pattern in patterns:
        text = re.sub(pattern, '', text)
    
    return text


test_cases = [
    "something. * - ",
    "example * text~~~~",
    "another example.",
    "text with asterisk*",
    "text with hyphen-",
    "multiple~~~",
    "mixed. * -~~~",
    "normal text"
]

for test_str in test_cases:
    cleaned = clean_right_end(test_str)
    print(f"'{test_str}' -> '{cleaned}'")


import re

value = "(400 MHz, DMSO-d6) Î´ 11.36-11.07 (m, 1H), 8.31 (s, 1H), 7.39- 7.09 (m, 6H), 5.97 (s, 1H), 3.79-3.59 (m, 2H), 3.18-2.82 (m, 5H), 1.93 (s, 4H) ppm, signal for the remaining four protons is covered with solvent"
ppm_pattern = re.compile(r'(.*\d+.*?\s*\bppm\b[\s.,;:]*)', re.IGNORECASE)

value_stripped = value
if len(value_stripped) > 50:
    search_text = value_stripped[50:]
    # Check for ppm first (preserve it)
    if "ppm" in search_text:
        ppm_match = ppm_pattern.search(search_text)
        if ppm_match:
            # Include "ppm" in the result - use group(1) to get the captured part
            match_end_pos = 50 + ppm_match.end(1)
            value_stripped = value_stripped[:match_end_pos].strip()
            # Clean up end characters: remove trailing punctuation and opening parenthesis
            value_stripped = re.sub(r'[,.;:!?(\s]+$', '', value_stripped)
            print(f"Result: '{value_stripped}'")
        else:
            print("ppm pattern not matched")
    else:
        print("ppm not found in search text")


