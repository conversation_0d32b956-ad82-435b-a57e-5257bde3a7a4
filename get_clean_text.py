import pandas as pd
import re
from bs4 import BeautifulSoup
import html
import warnings


def read_csv_file(file_path):
    """
    Read the CSV file and return a DataFrame
    """
    try:
        df = pd.read_csv(file_path)
        print(f"Successfully read CSV file: {file_path}")
        print(f"Shape: {df.shape}")
        return df
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return None


def load_cleaning_mappings(cleaning_file1, cleaning_file2):
    """
    Load the cleaning mappings from Excel files
    """
    try:
        # Load variable cleaning mappings
        variable_cleaning_df = pd.read_excel(cleaning_file1)
        symbols_cleaning_df = pd.read_excel(cleaning_file2)
        
        # Create dictionaries for mapping
        variable_mapping = dict(zip(variable_cleaning_df['ALTERNATE_FORM'], 
                                variable_cleaning_df['FINDING_TYPE']))
        symbols_mapping = dict(zip(symbols_cleaning_df['ALTERNATE_FORM'], 
                                symbols_cleaning_df['FINDING_TYPE']))
        
        print(f"Loaded {len(variable_mapping)} variable mappings")
        print(f"Loaded {len(symbols_mapping)} symbol mappings")
        
        return variable_mapping, symbols_mapping
    except Exception as e:
        print(f"Error loading cleaning mappings: {e}")
        return {}, {}

def remove_html_tags(text):
    """
    Remove HTML tags from text and return clean text
    """
    if pd.isna(text) or text == '':
        return text
    
    try:
        text_str = str(text).strip()
        
        # Check if the text actually contains HTML tags
        if '<' in text_str and '>' in text_str:
            # Suppress the specific warning about filename-like input
            with warnings.catch_warnings():
                warnings.filterwarnings("ignore", category=UserWarning, module="bs4")
                soup = BeautifulSoup(text_str, 'html.parser')
                clean_text = soup.get_text()
        else:
            # If no HTML tags, return as is
            clean_text = text_str
        
        # Also handle any remaining HTML entities
        clean_text = html.unescape(clean_text)
        
        return clean_text
    except Exception as e:
        print(f"Error removing HTML tags: {e}")
        return text


def replace_with_mappings(text, mapping_dict):
    """
    Replace text based on mapping dictionary
    """
    if pd.isna(text) or text == '':
        return text
    
    try:
        text_str = str(text)
        
        # Sort by length (longest first) to avoid partial replacements
        sorted_keys = sorted(mapping_dict.keys(), key=len, reverse=True)
        
        for alternate_form in sorted_keys:
            if alternate_form in text_str:
                finding_type = mapping_dict[alternate_form]
                text_str = text_str.replace(alternate_form, finding_type)
        
        return text_str
    except Exception as e:
        print(f"Error in mapping replacement: {e}")
        return text

def decode_encoded_characters(text):
    """
    Replace encoded characters with actual symbols
    """
    if pd.isna(text) or text == '':
        return text
    
    try:
        text_str = str(text)
        
        # Common encoded characters mapping
        encoded_chars = {
            # Add these additional UTF-8 encoding fixes to the encoded_chars dictionary
            'Â°': '°',   # degree symbol
            'Â±': '±',   # plus-minus
            'Â²': '²',   # superscript 2
            'Â³': '³',   # superscript 3
            'Â¼': '¼',   # one quarter
            'Â½': '½',   # one half
            'Â¾': '¾',   # three quarters
            'Â©': '©',   # copyright
            'Â®': '®',   # registered trademark
            'Â§': '§',   # section sign
            'Âµ': 'µ',   # micro sign


            "Â°C": "°C", # degree Celsius
            "Î´": "δ", # delta
            "&#948;" : "δ", # delta 
            "&#945;" : "a", # alpha
            "Î´" : "δ", # delta
            "Â°C;": "°C", # degree Celsius

            '&amp;': '&',
            '&lt;': '<',
            '&gt;': '>',
            '&quot;': '"',
            '&apos;': "'",
            '&nbsp;': ' ',
            '&#39;': "'",
            '&#34;': '"',
            '&#38;': '&',
            '&#60;': '<',
            '&#62;': '>',
            '&#160;': ' ',
            '%20': ' ',

            '%21': '!',
            '%22': '"',
            '%23': '#',
            '%24': '$',
            '%25': '%',
            '%26': '&',
            '%27': "'",
            '%28': '(',
            '%29': ')',
            '%2A': '*',
            '%2B': '+',
            '%2C': ',',
            '%2D': '-',
            '%2E': '.',
            '%2F': '/',
            '%3A': ':',
            '%3B': ';',
            '%3C': '<',
            '%3D': '=',
            '%3E': '>',
            '%3F': '?',
            '%40': '@',
            '%5B': '[',
            '%5C': '\\',
            '%5D': ']',
            '%5E': '^',
            '%5F': '_',
            '%60': '`',
            '%7B': '{',
            '%7C': '|',
            '%7D': '}',
            '%7E': '~',

            # Special characters
            '&#169;': '©',  # copyright
            '&#174;': '®',  # registered
            '&#8482;': '™', # trademark
            '&#167;': '§',  # section
            '&#182;': '¶',  # paragraph
            '&#8224;': '†', # dagger
            '&#8225;': '‡', # double dagger
            '&#8226;': '•', # bullet
            '&#8230;': '…', # ellipsis
            '&#8240;': '‰', # per mille
            '&#8242;': '′', # prime
            '&#8243;': '″', # double prime
            '&#8249;': '‹', # single left angle quotation
            '&#8250;': '›', # single right angle quotation
            '&#171;': '«',  # left double angle quotation
            '&#187;': '»',  # right double angle quotation
            '&#8216;': "'", # left single quotation
            '&#8217;': "'", # right single quotation
            '&#8218;': '‚', # single low-9 quotation
            '&#8220;': '"', # left double quotation
            '&#8221;': '"', # right double quotation
            '&#8222;': '„', # double low-9 quotation
            '&#8211;': '–', # en dash
            '&#8212;': '—', # em dash
            # Numeric HTML entities - Basic
            '&#39;': "'",
            '&#34;': '"',
            '&#38;': '&',
            '&#60;': '<',
            '&#62;': '>',
            '&#160;': ' ',
            # Greek letters
            '&#945;': 'α',  # alpha
            '&#946;': 'β',  # beta
            '&#947;': 'γ',  # gamma
            '&#948;': 'δ',  # delta
            '&#949;': 'ε',  # epsilon
            '&#950;': 'ζ',  # zeta
            '&#951;': 'η',  # eta
            '&#952;': 'θ',  # theta
            '&#953;': 'ι',  # iota
            '&#954;': 'κ',  # kappa
            '&#955;': 'λ',  # lambda
            '&#956;': 'μ',  # mu
            '&#957;': 'ν',  # nu
            '&#958;': 'ξ',  # xi
            '&#959;': 'ο',  # omicron
            '&#960;': 'π',  # pi
            '&#961;': 'ρ',  # rho
            '&#963;': 'σ',  # sigma
            '&#964;': 'τ',  # tau
            '&#965;': 'υ',  # upsilon
            '&#966;': 'φ',  # phi
            '&#967;': 'χ',  # chi
            '&#968;': 'ψ',  # psi
            '&#969;': 'ω',  # omega
            # Capital Greek letters
            '&#913;': 'Α',  # Alpha
            '&#914;': 'Β',  # Beta
            '&#915;': 'Γ',  # Gamma
            '&#916;': 'Δ',  # Delta
            '&#917;': 'Ε',  # Epsilon
            '&#918;': 'Ζ',  # Zeta
            '&#919;': 'Η',  # Eta
            '&#920;': 'Θ',  # Theta
            '&#921;': 'Ι',  # Iota
            '&#922;': 'Κ',  # Kappa
            '&#923;': 'Λ',  # Lambda
            '&#924;': 'Μ',  # Mu
            '&#925;': 'Ν',  # Nu
            '&#926;': 'Ξ',  # Xi
            '&#927;': 'Ο',  # Omicron
            '&#928;': 'Π',  # Pi
            '&#929;': 'Ρ',  # Rho
            '&#931;': 'Σ',  # Sigma
            '&#932;': 'Τ',  # Tau
            '&#933;': 'Υ',  # Upsilon
            '&#934;': 'Φ',  # Phi
            '&#935;': 'Χ',  # Chi
            '&#936;': 'Ψ',  # Psi
            '&#937;': 'Ω',  # Omega
            # Mathematical symbols
            '&#177;': '±',  # plus-minus
            '&#215;': '×',  # multiplication
            '&#247;': '÷',  # division
            '&#8804;': '≤', # less than or equal
            '&#8805;': '≥', # greater than or equal
            '&#8800;': '≠', # not equal
            '&#8776;': '≈', # approximately equal
            '&#8734;': '∞', # infinity
            '&#8730;': '√', # square root
            '&#8747;': '∫', # integral
            '&#8721;': '∑', # summation
            '&#8719;': '∏', # product
            '&#8706;': '∂', # partial derivative
            '&#8711;': '∇', # nabla
            '&#8712;': '∈', # element of
            '&#8713;': '∉', # not element of
            '&#8715;': '∋', # contains
            '&#8745;': '∩', # intersection
            '&#8746;': '∪', # union
            '&#8834;': '⊂', # subset
            '&#8835;': '⊃', # superset
            '&#8836;': '⊄', # not subset
            '&#8838;': '⊆', # subset or equal
            '&#8839;': '⊇', # superset or equal
            # Arrows
            '&#8592;': '←', # left arrow
            '&#8593;': '↑', # up arrow
            '&#8594;': '→', # right arrow
            '&#8595;': '↓', # down arrow
            '&#8596;': '↔', # left-right arrow
            '&#8597;': '↕', # up-down arrow
            '&#8656;': '⇐', # left double arrow
            '&#8657;': '⇑', # up double arrow
            '&#8658;': '⇒', # right double arrow
            '&#8659;': '⇓', # down double arrow
            '&#8660;': '⇔', # left-right double arrow
            
            # Currency symbols
            '&#162;': '¢',  # cent
            '&#163;': '£',  # pound
            '&#164;': '¤',  # currency
            '&#165;': '¥',  # yen
            '&#8364;': '€', # euro
            # Superscripts
            '&#8304;': '⁰', # superscript 0
            '&#185;': '¹',  # superscript 1
            '&#178;': '²',  # superscript 2
            '&#179;': '³',  # superscript 3
            '&#8308;': '⁴', # superscript 4
            '&#8309;': '⁵', # superscript 5
            '&#8310;': '⁶', # superscript 6
            '&#8311;': '⁷', # superscript 7
            '&#8312;': '⁸', # superscript 8
            '&#8313;': '⁹', # superscript 9
            # Subscripts
            '&#8320;': '₀', # subscript 0
            '&#8321;': '₁', # subscript 1
            '&#8322;': '₂', # subscript 2
            '&#8323;': '₃', # subscript 3
            '&#8324;': '₄', # subscript 4
            '&#8325;': '₅', # subscript 5
            '&#8326;': '₆', # subscript 6
            '&#8327;': '₇', # subscript 7
            '&#8328;': '₈', # subscript 8
            '&#8329;': '₉', # subscript 9
        }
        
        # Replace encoded characters
        for encoded, decoded in encoded_chars.items():
            text_str = text_str.replace(encoded, decoded)
        
        # Handle HTML entities using html.unescape
        text_str = html.unescape(text_str)
        
        return text_str
    except Exception as e:
        print(f"Error decoding characters: {e}")
        return text

def clean_text_columns(df, variable_mapping, symbols_mapping):
    """
    Clean the DATA_TEXT and FINDING_VALUE columns
    """
    try:
        # Create backup columns with _old suffix
        if 'DATA_TEXT' in df.columns:
            df['DATA_TEXT_old'] = df['DATA_TEXT'].copy()
        if 'FINDING_VALUE' in df.columns:
            df['FINDING_VALUE_old'] = df['FINDING_VALUE'].copy()
        
        # Clean DATA_TEXT column
        if 'DATA_TEXT' in df.columns:
            print("Cleaning DATA_TEXT column...")
            # Step 1: Remove HTML tags
            df['DATA_TEXT'] = df['DATA_TEXT'].apply(remove_html_tags)
            # Step 4: Decode encoded characters
            df['DATA_TEXT'] = df['DATA_TEXT'].apply(decode_encoded_characters)
            # Step 2: Apply variable mappings
            df['DATA_TEXT'] = df['DATA_TEXT'].apply(
                lambda x: replace_with_mappings(x, variable_mapping)
            )
            
            # Step 3: Apply symbol mappings
            df['DATA_TEXT'] = df['DATA_TEXT'].apply(
                lambda x: replace_with_mappings(x, symbols_mapping)
            )
            
            
        
        # Clean FINDING_VALUE column
        if 'FINDING_VALUE' in df.columns:
            print("Cleaning FINDING_VALUE column...")
            # Step 1: Remove HTML tags
            df['FINDING_VALUE'] = df['FINDING_VALUE'].apply(remove_html_tags)
            # Step 4: Decode encoded characters
            df['FINDING_VALUE'] = df['FINDING_VALUE'].apply(decode_encoded_characters)
            # Step 2: Apply variable mappings
            df['FINDING_VALUE'] = df['FINDING_VALUE'].apply(
                lambda x: replace_with_mappings(x, variable_mapping)
            )
            
            # Step 3: Apply symbol mappings
            df['FINDING_VALUE'] = df['FINDING_VALUE'].apply(
                lambda x: replace_with_mappings(x, symbols_mapping)
            )
        
        print("Text cleaning completed successfully!")
        return df
    
    except Exception as e:
        print(f"Error cleaning text columns: {e}")
        return df

def save_output_csv(df, output_filename="output_csv.csv"):
    """
    Save the cleaned DataFrame to CSV file
    """
    try:
        df.to_csv(output_filename, index=False)
        print(f"Output saved successfully to: {output_filename}")
        print(f"Output shape: {df.shape}")
    except Exception as e:
        print(f"Error saving output CSV: {e}")

def process_reaction_findings(input_file, cleaning_file1, cleaning_file2):
    """
    Main function to process the reaction findings data
    """
    print("Starting Reaction Findings 2025 processing...")
    
    # Step 1: Read the CSV file
    
    df = read_csv_file(input_file)
    
    if df is None:
        print("Failed to read input file. Exiting...")
        return
    
    # Step 2: Load cleaning mappings
    variable_mapping, symbols_mapping = load_cleaning_mappings(cleaning_file1, cleaning_file2)
    
    # Step 3: Clean the text columns
    df_cleaned = clean_text_columns(df, variable_mapping, symbols_mapping)
    
    # Step 4: Save the output
    save_output_csv(df_cleaned, input_file.replace(".csv", "_cleaned.csv"))
    
    print("Processing completed successfully!")
    return df_cleaned

if __name__ == "__main__":

    input_file = "UII_Shipment_982279_TAN_RxnFindings_20250623.csv"
    cleaning_file1 = "variable_cleaning_2.xlsx"
    cleaning_file2 = "variable_cleaning_symbols_2.xlsx"


    #main processing function
    result_df = process_reaction_findings(input_file, cleaning_file1, cleaning_file2)
